# Common integration tests
# Common integration tests (excluding those that depend on extract/transform/load)
set(COMMON_INTEGRATION_TEST_SOURCES
    config_file_management_test.cpp
    string_file_utilities_test.cpp
    validation_rules_engine_test.cpp
    error_recovery_integration_test.cpp
    # performance_benchmarking_test.cpp  # Temporarily disabled
)

add_executable(common_integration_tests ${COMMON_INTEGRATION_TEST_SOURCES})

target_link_libraries(common_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        # omop_extract  # Temporarily disabled due to build issues
        # omop_transform # Temporarily disabled due to dependencies
        # omop_load     # Temporarily disabled due to dependencies
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(common_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME common_integration_tests
    COMMAND common_integration_tests
)

set_tests_properties(common_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;common"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)

# Performance benchmarking test as separate executable
add_executable(common_performance_benchmarks performance_benchmarking_test.cpp)

# Error recovery performance benchmarks as separate executable
add_executable(error_recovery_performance_benchmarks error_recovery_performance_benchmark.cpp)

# Utility function performance benchmarks as separate executable
add_executable(utility_performance_benchmarks utility_performance_benchmark.cpp)

# ML anomaly detection integration tests as separate executable
add_executable(ml_anomaly_detection_integration_tests ml_anomaly_detection_integration_test.cpp)

target_link_libraries(common_performance_benchmarks
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        # omop_extract  # Temporarily disabled due to build issues
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(common_performance_benchmarks
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME common_performance_benchmarks
    COMMAND common_performance_benchmarks
)

set_tests_properties(common_performance_benchmarks PROPERTIES
    TIMEOUT 600
    LABELS "performance;benchmarks;common"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)

# Error recovery performance benchmarks configuration
target_link_libraries(error_recovery_performance_benchmarks
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(error_recovery_performance_benchmarks
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME error_recovery_performance_benchmarks
    COMMAND error_recovery_performance_benchmarks
)

set_tests_properties(error_recovery_performance_benchmarks PROPERTIES
    TIMEOUT 900
    LABELS "performance;benchmarks;error_recovery;common"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)

# Utility performance benchmarks configuration
target_link_libraries(utility_performance_benchmarks
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(utility_performance_benchmarks
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME utility_performance_benchmarks
    COMMAND utility_performance_benchmarks
)

set_tests_properties(utility_performance_benchmarks PROPERTIES
    TIMEOUT 600
    LABELS "performance;benchmarks;utility;common"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)

# ML anomaly detection integration tests configuration
target_link_libraries(ml_anomaly_detection_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(ml_anomaly_detection_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME ml_anomaly_detection_integration_tests
    COMMAND ml_anomaly_detection_integration_tests
)

set_tests_properties(ml_anomaly_detection_integration_tests PROPERTIES
    TIMEOUT 900
    LABELS "integration;ml;anomaly_detection;common"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)