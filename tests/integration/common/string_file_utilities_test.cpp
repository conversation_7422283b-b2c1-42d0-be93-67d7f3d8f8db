// Integration test for common utilities module
#include <gtest/gtest.h>
#include "common/utilities.h"
#include "test_helpers/integration_test_base.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <set>

namespace omop::test {

class UtilitiesIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        test_dir_ = std::filesystem::temp_directory_path() / "omop_utils_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
        IntegrationTestBase::TearDown();
    }

    std::filesystem::path test_dir_;
};

// Test string utilities with real-world patient data processing scenarios including internationalisation
TEST_F(UtilitiesIntegrationTest, StringUtilsRealWorldProcessing) {
    // Test processing patient names with various formats
    std::vector<std::string> patient_names = {
        "  <PERSON>  ",
        "MARY SMITH",
        "<PERSON>",
        "<PERSON>明 (<PERSON>)",
        "<PERSON><PERSON><PERSON>, <PERSON>",
        "<PERSON>, <PERSON>"
    };

    for (const auto& name : patient_names) {
        // Trim and normalize
        auto trimmed = omop::common::string_utils::trim(name);
        EXPECT_FALSE(trimmed.empty());
        EXPECT_EQ(trimmed.front(), trimmed.front()); // No leading space
        EXPECT_EQ(trimmed.back(), trimmed.back()); // No trailing space

        // Case conversion
        auto lower = omop::common::string_utils::to_lower(trimmed);
        auto upper = omop::common::string_utils::to_upper(trimmed);
        EXPECT_NE(lower, upper) << "Case conversion failed for: " << name;

        // Split by common delimiters
        if (trimmed.find(',') != std::string::npos) {
            auto parts = omop::common::string_utils::split(trimmed, ',');
            EXPECT_GE(parts.size(), 2) << "Failed to split: " << trimmed;
        }
    }

    // Test SQL escaping with potentially dangerous inputs
    std::vector<std::string> sql_inputs = {
        "Robert'); DROP TABLE patients;--",
        "O'Malley",
        "Test\\User",
        "Normal Name"
    };

    for (const auto& input : sql_inputs) {
        auto escaped = omop::common::string_utils::escape_sql(input);
        // Check that single quotes are properly escaped (doubled)
        if (input.find("'") != std::string::npos) {
            EXPECT_NE(escaped.find("''"), std::string::npos)
                << "SQL injection vulnerability for: " << input;
        }
        // Check that backslashes are properly escaped
        if (input.find("\\") != std::string::npos) {
            EXPECT_NE(escaped.find("\\\\"), std::string::npos)
                << "Backslash not properly escaped for: " << input;
        }
    }
}

// Test date/time utilities with medical data scenarios using UK formats
TEST_F(UtilitiesIntegrationTest, DateTimeUtilsMedicalScenarios) {
    // Test parsing various date formats from different systems with UK regionalisation
    std::vector<std::pair<std::string, std::string>> date_tests = {
        {"2023-01-15", "%Y-%m-%d"},              // ISO format
        {"15/01/2023", "%d/%m/%Y"},              // UK format (dd/mm/yyyy)
        {"15.01.2023", "%d.%m.%Y"},              // European format
        {"2023-01-15 14:30:00", "%Y-%m-%d %H:%M:%S"}, // Datetime (24-hour format)
        {"15 Jan 2023", "%d %b %Y"}              // UK text format
    };

    for (const auto& [date_str, format] : date_tests) {
        auto parsed = omop::common::date_utils::parse_date(date_str, format);
        ASSERT_TRUE(parsed.has_value()) << "Failed to parse: " << date_str;

        // Format back and verify
        auto formatted = omop::common::date_utils::format_date(*parsed, format);
        EXPECT_EQ(formatted, date_str) << "Round-trip formatting failed";
    }

    // Test age calculation for patient demographics
    auto birth_date = omop::common::date_utils::parse_date("1965-03-15", "%Y-%m-%d");
    auto ref_date = omop::common::date_utils::parse_date("2023-06-15", "%Y-%m-%d");
    ASSERT_TRUE(birth_date.has_value() && ref_date.has_value());

    int age = omop::common::date_utils::calculate_age(*birth_date, *ref_date);
    EXPECT_EQ(age, 58) << "Age calculation incorrect";

    // Test date validation for data quality checks
    EXPECT_TRUE(omop::common::date_utils::is_valid_date(2023, 2, 28));
    EXPECT_FALSE(omop::common::date_utils::is_valid_date(2023, 2, 30));
    EXPECT_TRUE(omop::common::date_utils::is_valid_date(2024, 2, 29)); // Leap year
    EXPECT_FALSE(omop::common::date_utils::is_valid_date(2023, 13, 1)); // Invalid month
    
    // Test UK-specific date operations
    auto current_time = omop::common::date_utils::current_timestamp("%d/%m/%Y %H:%M:%S");
    EXPECT_FALSE(current_time.empty());
    
    // Test ISO week calculation (important for UK health reporting)
    auto test_date = omop::common::date_utils::parse_date("2023-01-02", "%Y-%m-%d"); // Monday
    ASSERT_TRUE(test_date.has_value());
    int week_num = omop::common::date_utils::get_iso_week(*test_date);
    EXPECT_GT(week_num, 0);
    EXPECT_LE(week_num, 53);
}

// Test file utilities operations common in ETL data pipeline scenarios
TEST_F(UtilitiesIntegrationTest, FileUtilsETLPipeline) {
    // Create test CSV file
    std::string csv_content = "patient_id,name,birth_date\n"
                             "1001,John Doe,1980-01-15\n"
                             "1002,Jane Smith,1975-03-22\n";

    auto csv_path = test_dir_ / "patients.csv";
    ASSERT_TRUE(omop::common::file_utils::write_file(csv_path.string(), csv_content));

    // Verify file operations
    EXPECT_TRUE(omop::common::file_utils::file_exists(csv_path.string()));

    auto size = omop::common::file_utils::file_size(csv_path.string());
    ASSERT_TRUE(size.has_value());
    EXPECT_EQ(*size, csv_content.size());

    // Read file back
    auto read_content = omop::common::file_utils::read_file(csv_path.string());
    ASSERT_TRUE(read_content.has_value());
    EXPECT_EQ(*read_content, csv_content);

    // Test file extension detection
    EXPECT_EQ(omop::common::file_utils::get_extension(csv_path.string()), ".csv");
    EXPECT_EQ(omop::common::file_utils::get_basename(csv_path.string()), "patients");

    // Create directory structure for organized output
    auto output_dir = test_dir_ / "output" / "2023" / "06";
    ASSERT_TRUE(omop::common::file_utils::create_directory(output_dir.string()));
    EXPECT_TRUE(std::filesystem::exists(output_dir));

    // List files with pattern matching
    omop::common::file_utils::write_file((test_dir_ / "data1.csv").string(), "test");
    omop::common::file_utils::write_file((test_dir_ / "data2.csv").string(), "test");
    omop::common::file_utils::write_file((test_dir_ / "config.yaml").string(), "test");

    auto csv_files = omop::common::file_utils::list_files(test_dir_.string(), ".*\\.csv$");
    EXPECT_EQ(csv_files.size(), 3); // Including patients.csv

    // Test file operations
    auto src_file = test_dir_ / "source.txt";
    auto dst_file = test_dir_ / "destination.txt";
    omop::common::file_utils::write_file(src_file.string(), "test data");

    ASSERT_TRUE(omop::common::file_utils::copy_file(src_file.string(), dst_file.string()));
    EXPECT_TRUE(omop::common::file_utils::file_exists(dst_file.string()));

    // Clean up
    EXPECT_TRUE(omop::common::file_utils::delete_file(src_file.string()));
    EXPECT_FALSE(omop::common::file_utils::file_exists(src_file.string()));
}

// Test system utilities for monitoring system resources during ETL operations
TEST_F(UtilitiesIntegrationTest, SystemUtilsResourceMonitoring) {
    // Test environment variables
    omop::common::system_utils::set_env("OMOP_TEST_VAR", "test_value");
    auto env_value = omop::common::system_utils::get_env("OMOP_TEST_VAR");
    ASSERT_TRUE(env_value.has_value());
    EXPECT_EQ(*env_value, "test_value");

    // Test system information
    auto cpu_count = omop::common::system_utils::get_cpu_count();
    EXPECT_GT(cpu_count, 0) << "Invalid CPU count";

    auto memory = omop::common::system_utils::get_available_memory();
    EXPECT_GT(memory, 0) << "Invalid memory size";

    auto hostname = omop::common::system_utils::get_hostname();
    EXPECT_FALSE(hostname.empty()) << "Empty hostname";

    auto username = omop::common::system_utils::get_username();
    EXPECT_FALSE(username.empty()) << "Empty username";

    // Test process information
    auto pid = omop::common::system_utils::get_process_id();
    EXPECT_GT(pid, 0) << "Invalid process ID";

    // Test command execution (simple, safe command)
    #ifdef _WIN32
        auto result = omop::common::system_utils::execute_command("echo test");
    #else
        auto result = omop::common::system_utils::execute_command("echo test");
    #endif
    ASSERT_TRUE(result.has_value());
    EXPECT_NE(result->find("test"), std::string::npos);
}

// Test cryptographic utilities for securing patient data and maintaining privacy
TEST_F(UtilitiesIntegrationTest, CryptoUtilsDataSecurity) {
    // Test hashing for patient data de-identification
    std::string patient_ssn = "***********";
    auto md5_hash = omop::common::CryptoUtils::md5(patient_ssn);
    auto sha256_hash = omop::common::CryptoUtils::sha256(patient_ssn);

    EXPECT_EQ(md5_hash.length(), 32) << "Invalid MD5 hash length";
    EXPECT_EQ(sha256_hash.length(), 64) << "Invalid SHA256 hash length";
    EXPECT_NE(md5_hash, sha256_hash) << "Different algorithms produced same hash";

    // Verify deterministic hashing
    EXPECT_EQ(omop::common::CryptoUtils::md5(patient_ssn), md5_hash) << "Hash not deterministic";

    // Test base64 encoding for binary data storage
    std::vector<uint8_t> binary_data = {0x48, 0x65, 0x6C, 0x6C, 0x6F}; // "Hello"
    auto encoded = omop::common::CryptoUtils::base64_encode(binary_data);
    auto decoded = omop::common::CryptoUtils::base64_decode(encoded);

    EXPECT_EQ(decoded, binary_data) << "Base64 round-trip failed";

    // Test UUID generation for unique identifiers
    std::set<std::string> uuids;
    for (int i = 0; i < 1000; ++i) {
        auto uuid = omop::common::CryptoUtils::generate_uuid();
        EXPECT_EQ(uuid.length(), 36) << "Invalid UUID format";
        EXPECT_EQ(uuid[8], '-') << "Invalid UUID format";
        EXPECT_EQ(uuid[13], '-') << "Invalid UUID format";
        EXPECT_EQ(uuid[18], '-') << "Invalid UUID format";
        EXPECT_EQ(uuid[23], '-') << "Invalid UUID format";

        // Verify uniqueness
        EXPECT_TRUE(uuids.insert(uuid).second) << "Duplicate UUID generated";
    }
}

// Test validation utilities for ensuring healthcare data quality and standards compliance
TEST_F(UtilitiesIntegrationTest, ValidationUtilsDataQuality) {
    // Test email validation
    std::vector<std::pair<std::string, bool>> email_tests = {
        {"<EMAIL>", true},
        {"<EMAIL>", true},
        {"invalid.email@", false},
        {"@example.com", false},
        {"no-at-sign.com", false},
        {"<EMAIL>", true}
    };

    for (const auto& [email, expected] : email_tests) {
        EXPECT_EQ(omop::common::ValidationUtils::is_valid_email(email), expected)
            << "Email validation failed for: " << email;
    }

    // Test UK phone number validation
    std::vector<std::pair<std::string, bool>> phone_tests = {
        {"07700 900123", true},       // UK mobile
        {"020 7946 0958", true},      // London landline
        {"0161 496 0123", true},      // Manchester landline
        {"+44 20 7946 0958", true},   // International format
        {"0044 20 7946 0958", true},  // International format variant
        {"07700900123", true},        // No spaces
        {"1234", false},              // Too short
        {"0800 123 4567", true},      // Freephone
        {"0845 123 4567", true}       // Non-geographic
    };

    for (const auto& [phone, expected] : phone_tests) {
        bool result = omop::common::ValidationUtils::is_valid_uk_phone(phone);
        EXPECT_EQ(result, expected)
            << "UK phone validation failed for: " << phone;
    }

    // Test UK postal code validation
    std::vector<std::pair<std::string, bool>> postal_tests = {
        {"SW1A 1AA", true},    // Westminster
        {"M1 1AA", true},      // Manchester
        {"B33 8TH", true},     // Birmingham
        {"W1A 0AX", true},     // Oxford Street
        {"GIR 0AA", true},     // Special case (Girobank)
        {"SW1A1AA", true},     // No space variant
        {"INVALID", false},    // Invalid format
        {"12345", false},      // US format should fail UK validation
        {"A1 2BC", true},      // Valid UK format
        {"AB12 3CD", true}     // Another valid UK format
    };

    for (const auto& [postal, expected] : postal_tests) {
        EXPECT_EQ(omop::common::ValidationUtils::is_valid_uk_postcode(postal), expected)
            << "UK postal code validation failed for: " << postal;
    }

    // Test JSON validation for API responses
    std::string valid_json = R"({"patient_id": 1001, "name": "John Doe"})";
    std::string invalid_json = R"({"patient_id": 1001, "name": )"; // Missing value

    EXPECT_TRUE(omop::common::ValidationUtils::is_valid_json(valid_json));
    EXPECT_FALSE(omop::common::ValidationUtils::is_valid_json(invalid_json));

    // Test SQL identifier validation for dynamic queries
    std::vector<std::pair<std::string, bool>> sql_tests = {
        {"patient_table", true},
        {"table123", true},
        {"123table", false}, // Starts with number
        {"table-name", false}, // Contains hyphen
        {"table name", false}, // Contains space
        {"DROP_TABLE", true} // Valid identifier despite being SQL keyword
    };

    for (const auto& [identifier, expected] : sql_tests) {
        EXPECT_EQ(omop::common::ValidationUtils::is_valid_sql_identifier(identifier), expected)
            << "SQL identifier validation failed for: " << identifier;
    }
}

// Test performance monitoring utilities for ETL process optimisation
TEST_F(UtilitiesIntegrationTest, PerformanceUtilsMonitoring) {
    // Test timer for operation timing
    omop::common::PerformanceUtils::Timer timer;

    // Simulate some work
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    double elapsed_ms = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed_ms, 100.0) << "Timer measurement too low";
    EXPECT_LE(elapsed_ms, 150.0) << "Timer measurement too high";

    // Reset and measure again
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    double elapsed_s = timer.elapsed_seconds();
    EXPECT_GE(elapsed_s, 0.05) << "Timer measurement too low";
    EXPECT_LE(elapsed_s, 0.075) << "Timer measurement too high";

    // Test memory tracking
    omop::common::PerformanceUtils::MemoryTracker mem_tracker;

    size_t initial_usage = mem_tracker.current_usage();
    // Initial usage might be 0 on some systems, which is valid

    // Allocate some memory
    std::vector<std::vector<int>> large_data(1000, std::vector<int>(1000, 42));

    size_t after_alloc = mem_tracker.current_usage();
    EXPECT_GE(after_alloc, initial_usage) << "Memory usage should not decrease after allocation";

    size_t peak = mem_tracker.peak_usage();
    EXPECT_GE(peak, after_alloc) << "Peak usage should be at least current usage";

    // Test formatting utilities
    std::string bytes_str = omop::common::PerformanceUtils::format_bytes(1536);
    EXPECT_EQ(bytes_str, "1.50 KB");

    bytes_str = omop::common::PerformanceUtils::format_bytes(1048576);
    EXPECT_EQ(bytes_str, "1.00 MB");

    std::string duration_str = omop::common::PerformanceUtils::format_duration(3725.5);
    EXPECT_EQ(duration_str, "1h 2m 5s");

    // Test throughput calculation
    double throughput = omop::common::PerformanceUtils::calculate_throughput(10000, 2.5);
    EXPECT_DOUBLE_EQ(throughput, 4000.0) << "Incorrect throughput calculation";
}

// Test UK-specific healthcare validation utilities
TEST_F(UtilitiesIntegrationTest, UKHealthcareValidation) {
    // Test NHS number validation with valid NHS numbers
    std::vector<std::pair<std::string, bool>> nhs_tests = {
        {"************", true},      // Valid NHS number with spaces (checksum: 4)
        {"**********", true},        // Valid NHS number without spaces
        {"************", true},      // Another valid NHS number (checksum: 9)
        {"************", false},     // Invalid checksum (should be 9, not 8)
        {"123456789", false},        // Too short
        {"12345678901", false},      // Too long
        {"123A567890", false},       // Contains letter
        {"************", false}      // Invalid checksum (should be 0, not 1)
    };

    for (const auto& [nhs, expected] : nhs_tests) {
        EXPECT_EQ(omop::common::MedicalUtils::is_valid_nhs_number(nhs), expected)
            << "NHS number validation failed for: " << nhs;
    }

    // Test UK National Insurance number validation
    std::vector<std::pair<std::string, bool>> ni_tests = {
        {"*********", true},         // Valid NI number
        {"*********", true},         // Another valid NI number
        {"*********", false},        // Invalid prefix (BG)
        {"*********", false},        // Invalid prefix (GB)
        {"AB12345C", false},         // Too short
        {"AB1234567C", false},       // Too long
        {"1B123456C", false},        // First char not letter
        {"AB12345", false},          // Missing suffix letter
        {"*********", false}         // Invalid prefix (ZZ)
    };

    for (const auto& [ni, expected] : ni_tests) {
        EXPECT_EQ(omop::common::MedicalUtils::is_valid_uk_ni_number(ni), expected)
            << "UK NI number validation failed for: " << ni;
    }

    // Test UK postcode validation with all valid formats
    std::vector<std::pair<std::string, bool>> postcode_tests = {
        {"M1 1AA", true},            // A9 9AA format (Manchester)
        {"M60 1NW", true},           // A99 9AA format (Manchester)
        {"CR0 2YR", true},           // AA9 9AA format (Croydon)
        {"DN55 1PT", true},          // AA99 9AA format (Doncaster)
        {"W1A 0AX", true},           // A9A 9AA format (London)
        {"EC1A 1BB", true},          // AA9A 9AA format (London)
        {"SW1A 1AA", true},          // AA9A 9AA format (Westminster)
        {"SE1 9GF", true},           // AA9 9AA format (London)
        {"E1 6AN", true},            // A9 9AA format (London)
        {"B33 8TH", true},           // A99 9AA format (Birmingham)
        {"W1S 4EX", true},           // A9A 9AA format (London West End)
        {"EC1V 2NX", true},          // AA9A 9AA format (London City)
        {"INVALID", false},          // Invalid format
        {"M1 1AAB", false},          // Too long
        {"M1", false},               // Too short
        {"123 456", false},          // All numbers
        {"", false}                  // Empty string
    };

    for (const auto& [postcode, expected] : postcode_tests) {
        EXPECT_EQ(omop::common::ValidationUtils::is_valid_uk_postcode(postcode), expected)
            << "UK postcode validation failed for: " << postcode;
    }

    // Test UK phone number validation
    std::vector<std::pair<std::string, bool>> phone_tests = {
        {"07700 900123", true},      // Valid UK mobile
        {"07700900123", true},       // Valid UK mobile without spaces
        {"+44 7700 900123", true},   // Valid UK mobile international format
        {"0044 7700 900123", true},  // Valid UK mobile international format
        {"020 7946 0958", true},     // Valid London landline
        {"0117 496 0123", true},     // Valid Bristol landline
        {"0161 496 0123", true},     // Valid Manchester landline
        {"(020) 7946-0958", true},   // Valid with brackets and hyphens
        {"01234 567890", true},      // Valid geographic number
        {"0800 1234567", true},      // Valid freephone (11 digits)
        {"123456789", false},        // Too short
        {"01234567890123", false},   // Too long
        {"1234567890", false},       // Doesn't start with 0
        {"", false}                  // Empty string
    };

    for (const auto& [phone, expected] : phone_tests) {
        EXPECT_EQ(omop::common::ValidationUtils::is_valid_uk_phone(phone), expected)
            << "UK phone validation failed for: " << phone;
    }

    // Test SNOMED CT code validation
    std::vector<std::pair<std::string, bool>> snomed_tests = {
        {"195967001", true},         // Valid SNOMED CT concept ID
        {"22298006", true},          // Another valid concept ID
        {"123456789012345678", true}, // 18 digits (max length)
        {"123456", true},            // 6 digits (min length)
        {"12345", false},            // Too short
        {"1234567890123456789", false}, // Too long
        {"123A56", false},           // Contains letter
        {"", false}                  // Empty string
    };

    for (const auto& [snomed, expected] : snomed_tests) {
        EXPECT_EQ(omop::common::MedicalUtils::is_valid_snomed_code(snomed), expected)
            << "SNOMED CT validation failed for: " << snomed;
    }

    // Test Read code validation (UK legacy clinical coding)
    std::vector<std::pair<std::string, bool>> read_tests = {
        {"G20..", true},             // Valid Read code
        {"14A6.", true},             // Another valid Read code
        {"C10z.", true},             // Third valid Read code
        {"XE2mO", true},             // Valid Read code without dots
        {"AB", true},                // Valid 2-character READ code
        {"ABCDEFGH", false},         // Too long
        {"G20@.", false},            // Invalid character
        {"", false}                  // Empty string
    };

    for (const auto& [read, expected] : read_tests) {
        EXPECT_EQ(omop::common::MedicalUtils::is_valid_read_code(read), expected)
            << "Read code validation failed for: " << read;
    }

    // Test UK date format validation (DD/MM/YYYY format)
    std::vector<std::pair<std::string, bool>> uk_date_tests = {
        {"25/12/2023", true},        // Valid UK Christmas date
        {"29/02/2024", true},        // Valid leap year date
        {"31/01/2023", true},        // Valid end of month
        {"01/13/2023", false},       // Invalid month (13)
        {"32/01/2023", false},       // Invalid day (32)
        {"29/02/2023", false},       // Invalid leap year
        {"2023-12-25", false},       // Wrong format (ISO)
        {"", false}                  // Empty string
    };

    for (const auto& [date, expected] : uk_date_tests) {
        EXPECT_EQ(omop::common::ValidationUtils::is_valid_date_format(date, "%d/%m/%Y"), expected)
            << "UK date format validation failed for: " << date;
    }

    // Test comprehensive UK healthcare workflow validation
    struct UKPatientRecord {
        std::string nhs_number;
        std::string ni_number;
        std::string postcode;
        std::string phone;
        std::string birth_date;
        bool expected_valid;
    };

    std::vector<UKPatientRecord> patient_records = {
        {"************", "*********", "M1 1AA", "07700 900123", "25/12/1985", true},
        {"************", "*********", "SW1A 1AA", "020 7946 0958", "15/06/1970", true},
        {"************", "*********", "M1 1AA", "07700 900123", "25/12/1985", false}, // Invalid NHS
        {"************", "*********", "M1 1AA", "07700 900123", "25/12/1985", false}, // Invalid NI
        {"************", "*********", "INVALID", "07700 900123", "25/12/1985", false}, // Invalid postcode
        {"************", "*********", "M1 1AA", "123456789", "25/12/1985", false},     // Invalid phone
        {"************", "*********", "M1 1AA", "07700 900123", "32/12/1985", false}   // Invalid date
    };

    int valid_patients = 0;
    for (const auto& record : patient_records) {
        bool is_valid = omop::common::MedicalUtils::is_valid_nhs_number(record.nhs_number) &&
                       omop::common::MedicalUtils::is_valid_uk_ni_number(record.ni_number) &&
                       omop::common::ValidationUtils::is_valid_uk_postcode(record.postcode) &&
                       omop::common::ValidationUtils::is_valid_uk_phone(record.phone) &&
                       omop::common::ValidationUtils::is_valid_date_format(record.birth_date, "%d/%m/%Y");
        
        EXPECT_EQ(is_valid, record.expected_valid)
            << "UK patient record validation failed for NHS: " << record.nhs_number;
        
        if (is_valid) {
            valid_patients++;
        }
    }

    LOG_INFO(logger_, "UK Healthcare Validation Summary: {} valid patients out of {} total records", 
             valid_patients, patient_records.size());
    
    // Verify we have the expected number of valid UK patient records
    EXPECT_EQ(valid_patients, 2) << "Expected exactly 2 valid UK patient records";
}

// Test integrated workflow simulating complete UK healthcare ETL pipeline scenarios
TEST_F(UtilitiesIntegrationTest, IntegratedETLWorkflow) {
    // Simulate complete ETL workflow using utilities

    // 1. Setup environment
    omop::common::system_utils::set_env("OMOP_ETL_HOME", test_dir_.string());

    // 2. Create directory structure
    auto input_dir = test_dir_ / "input";
    auto output_dir = test_dir_ / "output";
    auto log_dir = test_dir_ / "logs";

    ASSERT_TRUE(omop::common::file_utils::create_directory(input_dir.string()));
    ASSERT_TRUE(omop::common::file_utils::create_directory(output_dir.string()));
    ASSERT_TRUE(omop::common::file_utils::create_directory(log_dir.string()));

    // 3. Generate test data with timing
    omop::common::PerformanceUtils::Timer gen_timer;

    // Create patient data
    std::stringstream patient_data;
    patient_data << "patient_id,ssn,name,email,phone,birth_date\n";

    for (int i = 1; i <= 100; ++i) {
        std::string ssn = std::format("123-45-{:04d}", i);
        std::string name = std::format("Patient_{:03d}", i);
        std::string email = std::format("patient{}@example.com", i);
        std::string phone = std::format("(617) 555-{:04d}", i);
        std::string birth_date = std::format("19{:02d}-{:02d}-15",
            50 + (i % 50), (i % 12) + 1);

        patient_data << i << "," << ssn << "," << name << ","
                    << email << "," << phone << "," << birth_date << "\n";
    }

    auto input_file = input_dir / "patients_raw.csv";
    ASSERT_TRUE(omop::common::file_utils::write_file(input_file.string(), patient_data.str()));

    double gen_time = gen_timer.elapsed_seconds();
    LOG_INFO(logger_, "Generated test data in {:.3f} seconds", gen_time);

    // 4. Process and validate data
    omop::common::PerformanceUtils::Timer proc_timer;

    auto content = omop::common::file_utils::read_file(input_file.string());
    ASSERT_TRUE(content.has_value());

    auto lines = omop::common::string_utils::split(*content, '\n');
    EXPECT_EQ(lines.size(), 101); // Header + 100 records (split doesn't include trailing empty line)

    // Process each record
    int valid_records = 0;
    int invalid_records = 0;

    for (size_t i = 1; i < lines.size() - 1; ++i) { // Skip header and empty line
        auto fields = omop::common::string_utils::split(lines[i], ',');
        if (fields.size() != 6) continue;

        // Validate fields
        bool valid = true;

        // Validate email
        if (!omop::common::ValidationUtils::is_valid_email(fields[3])) {
            valid = false;
        }

        // Validate phone
        if (!omop::common::ValidationUtils::is_valid_phone(fields[4], "US")) {
            valid = false;
        }

        // Validate date format
        if (!omop::common::ValidationUtils::is_valid_date_format(fields[5], "%Y-%m-%d")) {
            valid = false;
        }

        if (valid) {
            valid_records++;

            // Hash SSN for privacy
            fields[1] = omop::common::CryptoUtils::sha256(fields[1]);

            // Normalize name
            fields[2] = omop::common::string_utils::to_upper(omop::common::string_utils::trim(fields[2]));
        } else {
            invalid_records++;
        }
    }

    double proc_time = proc_timer.elapsed_seconds();
    double throughput = omop::common::PerformanceUtils::calculate_throughput(100, proc_time);

    LOG_INFO(logger_, "Processed {} records in {:.3f} seconds ({:.1f} records/sec)",
             valid_records + invalid_records, proc_time, throughput);
    LOG_INFO(logger_, "Valid: {}, Invalid: {}", valid_records, invalid_records);

    // 5. Generate summary report
    std::stringstream report;
    report << "ETL Processing Report\n";
    report << "====================\n";
    report << "Environment: " << omop::common::system_utils::get_hostname() << "\n";
    report << "User: " << omop::common::system_utils::get_username() << "\n";
    report << "Timestamp: " << omop::common::date_utils::current_timestamp() << "\n";
    report << "CPU Cores: " << omop::common::system_utils::get_cpu_count() << "\n";
    report << "Available Memory: " << omop::common::PerformanceUtils::format_bytes(
        omop::common::system_utils::get_available_memory()) << "\n";
    report << "\nProcessing Summary:\n";
    report << "- Input File: " << omop::common::file_utils::get_basename(input_file.string())
           << omop::common::file_utils::get_extension(input_file.string()) << "\n";
    report << "- File Size: " << omop::common::PerformanceUtils::format_bytes(
        *omop::common::file_utils::file_size(input_file.string())) << "\n";
    report << "- Total Records: " << (valid_records + invalid_records) << "\n";
    report << "- Valid Records: " << valid_records << "\n";
    report << "- Invalid Records: " << invalid_records << "\n";
    report << "- Processing Time: " <<
        omop::common::PerformanceUtils::format_duration(proc_time) << "\n";
    report << "- Throughput: " << std::format("{:.1f}", throughput)
           << " records/sec\n";

    auto report_file = output_dir / "processing_report.txt";
    ASSERT_TRUE(omop::common::file_utils::write_file(report_file.string(), report.str()));

    // Verify report was created
    EXPECT_TRUE(omop::common::file_utils::file_exists(report_file.string()));

    // 6. Create audit log with hashed identifiers
    std::stringstream audit_log;
    audit_log << "Audit Log - " << omop::common::date_utils::current_timestamp() << "\n";
    audit_log << "Session ID: " << omop::common::CryptoUtils::generate_uuid() << "\n";
    audit_log << "Process ID: " << omop::common::system_utils::get_process_id() << "\n";

    auto log_file = log_dir / "audit.log";
    ASSERT_TRUE(omop::common::file_utils::write_file(log_file.string(), audit_log.str()));
}

} // namespace omop::test