# Common library CMakeLists.txt

add_library(omop_common STATIC)

set(COMMON_SOURCES
    alert_manager.cpp
    async_logger.cpp
    configuration.cpp
    config/schema_validator.cpp
    # config/configuration_performance.cpp  # Temporarily disabled due to build issues
    # config/configuration_backup.cpp       # Temporarily disabled due to build issues
    # config/configuration_audit.cpp        # Temporarily disabled due to build issues
    # config/configuration_environment.cpp  # Temporarily disabled due to build issues
    # config/configuration_cache.cpp        # Temporarily disabled due to build issues
    error_recovery.cpp
    exceptions.cpp
    health_monitor.cpp
    http_client.cpp
    logging.cpp
    metrics_collector.cpp
    performance_monitor.cpp
    tracing_manager.cpp
    utilities.cpp
    validation.cpp
    # ml_anomaly_detectors.cpp         # Temporarily disabled due to build issues
    # ml_anomaly_integration.cpp       # Temporarily disabled due to build issues
)

set(COMMON_HEADERS
    alert_manager.h
    async_logger.h
    configuration.h
    config/configuration_loader.h
    config/configuration_manager.h
    config/configuration_validator.h
    config/configuration_watcher.h
    # config/configuration_performance.h    # Temporarily disabled due to build issues
    # config/configuration_backup.h         # Temporarily disabled due to build issues
    # config/configuration_audit.h          # Temporarily disabled due to build issues
    # config/configuration_environment.h    # Temporarily disabled due to build issues
    # config/configuration_cache.h          # Temporarily disabled due to build issues
    config/schema_validator.h
    error_recovery.h
    exceptions.h
    health_monitor.h
    http_client.h
    logging.h
    metrics_collector.h
    performance_monitor.h
    tracing_manager.h
    utilities.h
    validation.h
    validation_cache.h
    # ml_anomaly_detectors.h           # Temporarily disabled due to build issues
    # ml_anomaly_integration.h         # Temporarily disabled due to build issues
)

# Add utils subdirectory for dedicated utility classes
add_subdirectory(utils)

target_sources(omop_common PRIVATE ${COMMON_SOURCES})

# Create spdlog alias target if it doesn't exist
if(TARGET spdlog AND NOT TARGET spdlog::spdlog)
    add_library(spdlog::spdlog ALIAS spdlog)
endif()

omop_configure_library(omop_common
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        yaml-cpp::yaml-cpp
        omop_common_utils
    PRIVATE_DEPS
        nlohmann_json
        fmt
        spdlog
        OpenSSL::SSL
        OpenSSL::Crypto
        Threads::Threads
        ${UUID_LIBRARIES}
    HEADERS
        ${COMMON_HEADERS}
)

# Explicitly add include directories for dependencies
target_include_directories(omop_common PRIVATE
    ${CMAKE_BINARY_DIR}/_deps/spdlog-src/include
    ${CMAKE_BINARY_DIR}/_deps/nlohmann_json-src/include
    ${CMAKE_BINARY_DIR}/_deps/fmt-src/include
)