/**
 * @file utilities.cpp
 * @brief Implementation of utility functions for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "utilities.h"
#include "logging.h"
#include <any>
#include <limits>

#include <fstream>
#include <random>
#include <cctype>
#include <ctime>
#include <iomanip>
#include <iostream>
#include <regex>
#include <sstream>
#include <iterator>
#include <algorithm>
#include <stdexcept>
#include <chrono>
#include <filesystem>
#include <unordered_map>
#include <vector>
#include <string>
#include <optional>
#include <functional>
#include <map>
#include <set>
#include <queue>
#include <condition_variable>
#include <future>
#include <thread>
#include <atomic>
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <uuid/uuid.h>
#include <nlohmann/json.hpp>
#include <cstdlib>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <array>
#include <memory>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#elif defined(__linux__)
#include <sys/resource.h>
#include <sys/sysinfo.h>
#elif defined(__APPLE__)
#include <sys/resource.h>
#include <sys/types.h>
#include <sys/sysctl.h>
#include <mach/mach.h>
#endif

namespace omop::common {

// Thread-local random generator for better performance and thread safety
// Use a safer initialization to avoid floating point exceptions
thread_local std::mt19937 tl_random_gen{[]() -> std::mt19937::result_type {
    try {
        return std::random_device{}();
    } catch (...) {
        // Fallback to time-based seed if random_device fails
        return static_cast<std::mt19937::result_type>(
            std::chrono::steady_clock::now().time_since_epoch().count()
        );
    }
}()};

// Optimized any_to_string with type lookup cache for high-throughput scenarios
std::string any_to_string(const std::any& value) {
    if (!value.has_value()) {
        return "";
    }

    const std::type_info& type = value.type();

    // Use fast type hash lookup for better performance in high-throughput scenarios
    static const std::unordered_map<std::size_t, std::function<std::string(const std::any&)>> type_converters = {
        {typeid(std::string).hash_code(), [](const std::any& v) { return std::any_cast<std::string>(v); }},
        {typeid(int).hash_code(), [](const std::any& v) { return std::to_string(std::any_cast<int>(v)); }},
        {typeid(long).hash_code(), [](const std::any& v) { return std::to_string(std::any_cast<long>(v)); }},
        {typeid(long long).hash_code(), [](const std::any& v) { return std::to_string(std::any_cast<long long>(v)); }},
        {typeid(unsigned int).hash_code(), [](const std::any& v) { return std::to_string(std::any_cast<unsigned int>(v)); }},
        {typeid(unsigned long).hash_code(), [](const std::any& v) { return std::to_string(std::any_cast<unsigned long>(v)); }},
        {typeid(unsigned long long).hash_code(), [](const std::any& v) { return std::to_string(std::any_cast<unsigned long long>(v)); }},
        {typeid(float).hash_code(), [](const std::any& v) { return std::to_string(std::any_cast<float>(v)); }},
        {typeid(double).hash_code(), [](const std::any& v) { return std::to_string(std::any_cast<double>(v)); }},
        {typeid(bool).hash_code(), [](const std::any& v) { return std::any_cast<bool>(v) ? "true" : "false"; }},
        {typeid(char).hash_code(), [](const std::any& v) { return std::string(1, std::any_cast<char>(v)); }}
    };

    try {
        auto hash = type.hash_code();
        auto it = type_converters.find(hash);
        if (it != type_converters.end()) {
            return it->second(value);
        } else {
            // For unknown types, return type name
            return std::string("<") + type.name() + ">";
        }
    } catch (const std::bad_any_cast& e) {
        return std::string("<bad_cast: ") + type.name() + ">";
    }
}

double any_to_double(const std::any& value) {
    if (!value.has_value()) {
        return 0.0;
    }

    const std::type_info& type = value.type();

    try {
        if (type == typeid(double)) {
            return std::any_cast<double>(value);
        } else if (type == typeid(float)) {
            return static_cast<double>(std::any_cast<float>(value));
        } else if (type == typeid(int)) {
            return static_cast<double>(std::any_cast<int>(value));
        } else if (type == typeid(long)) {
            return static_cast<double>(std::any_cast<long>(value));
        } else if (type == typeid(long long)) {
            return static_cast<double>(std::any_cast<long long>(value));
        } else if (type == typeid(unsigned int)) {
            return static_cast<double>(std::any_cast<unsigned int>(value));
        } else if (type == typeid(unsigned long)) {
            return static_cast<double>(std::any_cast<unsigned long>(value));
        } else if (type == typeid(unsigned long long)) {
            return static_cast<double>(std::any_cast<unsigned long long>(value));
        } else if (type == typeid(bool)) {
            return std::any_cast<bool>(value) ? 1.0 : 0.0;
        } else if (type == typeid(std::string)) {
            const std::string& str = std::any_cast<std::string>(value);
            try {
                // Handle UK number format (remove commas)
                std::string cleaned_str = str;
                cleaned_str.erase(std::remove(cleaned_str.begin(), cleaned_str.end(), ','), cleaned_str.end());
                return std::stod(cleaned_str);
            } catch (...) {
                return 0.0;
            }
        } else if (type == typeid(char)) {
            return static_cast<double>(std::any_cast<char>(value));
        }
        return 0.0;
    } catch (const std::bad_any_cast&) {
        return 0.0;
    }
}


// CryptoUtils implementation
std::string CryptoUtils::md5(const std::string& data) {
    unsigned char digest[EVP_MAX_MD_SIZE];
    unsigned int digest_len;
    
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        throw std::runtime_error("Failed to create EVP_MD_CTX");
    }
    
    if (EVP_DigestInit_ex(mdctx, EVP_md5(), nullptr) != 1 ||
        EVP_DigestUpdate(mdctx, data.c_str(), data.length()) != 1 ||
        EVP_DigestFinal_ex(mdctx, digest, &digest_len) != 1) {
        EVP_MD_CTX_free(mdctx);
        throw std::runtime_error("MD5 computation failed");
    }
    
    EVP_MD_CTX_free(mdctx);

    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (unsigned int i = 0; i < digest_len; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(digest[i]);
    }

    return ss.str();
}

std::string CryptoUtils::sha256(const std::string& data) {
    unsigned char digest[SHA256_DIGEST_LENGTH];
    
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        throw std::runtime_error("Failed to create EVP_MD_CTX");
    }
    
    if (EVP_DigestInit_ex(mdctx, EVP_sha256(), nullptr) != 1 ||
        EVP_DigestUpdate(mdctx, data.c_str(), data.length()) != 1 ||
        EVP_DigestFinal_ex(mdctx, digest, nullptr) != 1) {
        EVP_MD_CTX_free(mdctx);
        throw std::runtime_error("SHA256 computation failed");
    }
    
    EVP_MD_CTX_free(mdctx);

    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(digest[i]);
    }

    return ss.str();
}

std::string CryptoUtils::base64_encode(const std::vector<uint8_t>& data) {
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO* bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);
    BIO_write(bio, data.data(), data.size());
    BIO_flush(bio);

    BUF_MEM* buffer_ptr;
    BIO_get_mem_ptr(bio, &buffer_ptr);

    std::string result(buffer_ptr->data, buffer_ptr->length);
    BIO_free_all(bio);

    return result;
}

std::vector<uint8_t> CryptoUtils::base64_decode(const std::string& encoded) {
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO* bio = BIO_new_mem_buf(encoded.c_str(), encoded.length());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);

    std::vector<uint8_t> result(encoded.length());
    int decoded_length = BIO_read(bio, result.data(), encoded.length());
    BIO_free_all(bio);

    result.resize(decoded_length);
    return result;
}

std::string CryptoUtils::generate_uuid() {
    uuid_t uuid;
    
    // Use uuid_generate_random for more entropy and to avoid potential 
    // MAC address dependencies that could cause issues
    uuid_generate_random(uuid);

    char uuid_str[37];
    uuid_unparse_lower(uuid, uuid_str);

    return std::string(uuid_str);
}

std::vector<uint8_t> CryptoUtils::random_bytes(size_t length) {
    std::vector<uint8_t> result(length);
    
    if (RAND_bytes(result.data(), length) != 1) {
        // Fall back to C++ random if OpenSSL fails
        std::uniform_int_distribution<> dis(0, 255);
        
        // Use thread-local generator
        std::lock_guard<std::mutex> lock(random_mutex);
        
        // Log warning about fallback
        std::cerr << "Warning: OpenSSL RAND_bytes failed, using C++ random fallback\n";

        for (size_t i = 0; i < length; ++i) {
            result[i] = static_cast<uint8_t>(dis(tl_random_gen));
        }
    }

    return result;
}

// Static mutex for thread safety
std::mutex CryptoUtils::random_mutex;

// PerformanceUtils::MemoryTracker implementation
std::atomic<size_t> PerformanceUtils::MemoryTracker::global_peak_usage_{0};

// ValidationUtils implementation
bool ValidationUtils::is_valid_email(const std::string& email) {
    const std::regex pattern(R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)");
    return std::regex_match(email, pattern);
}

bool ValidationUtils::is_valid_url(const std::string& url) {
    // Using simpler regex pattern from isValidURL for consistency
    const std::regex pattern(R"((https?|ftp)://[^\s/$.?#].[^\s]*)");
    return std::regex_match(url, pattern);
}

bool ValidationUtils::is_valid_ip(const std::string& ip) {
    // IPv4 pattern
    const std::regex ipv4_pattern(
        R"(^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)");

    // Simplified IPv6 pattern
    const std::regex ipv6_pattern(
        R"(^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::1|::)$)");

    return std::regex_match(ip, ipv4_pattern) || std::regex_match(ip, ipv6_pattern);
}

bool ValidationUtils::is_valid_phone(const std::string& phone, const std::string& country_code) {
    if (country_code == "US") {
        // US phone number validation with cleaning (synced from isValidPhoneNumber)
        std::string cleaned = string_utils::replace_all(phone, " ", "");
        cleaned = string_utils::replace_all(cleaned, "-", "");
        cleaned = string_utils::replace_all(cleaned, "(", "");
        cleaned = string_utils::replace_all(cleaned, ")", "");
        cleaned = string_utils::replace_all(cleaned, "+1", "");

        // Check if it's a 10-digit number
        if (cleaned.length() == 10 && std::all_of(cleaned.begin(), cleaned.end(), ::isdigit)) {
            return true;
        }

        // Check if it's a 11-digit number starting with 1
        if (cleaned.length() == 11 && cleaned[0] == '1' &&
            std::all_of(cleaned.begin() + 1, cleaned.end(), ::isdigit)) {
            return true;
        }
        
        // Also support regex pattern for flexibility
        const std::regex pattern(R"(^\+?1?\s*\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$)");
        return std::regex_match(phone, pattern);
    }
    else if (country_code == "GB" || country_code == "UK") {
        return is_valid_uk_phone(phone);
    }
    
    // For unsupported countries, return false or implement additional patterns
    return false;
}

bool ValidationUtils::is_valid_postal_code(const std::string& postal_code,
                                          const std::string& country_code) {
    if (country_code == "US") {
        const std::regex pattern(R"(^\d{5}(-\d{4})?$)");
        return std::regex_match(postal_code, pattern);
    } else if (country_code == "CA") {
        const std::regex pattern(R"(^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$)");
        return std::regex_match(postal_code, pattern);
    } else if (country_code == "GB" || country_code == "UK") {
        return is_valid_uk_postcode(postal_code);
    }
    // Add more country-specific patterns as needed
    return false;
}

bool ValidationUtils::is_valid_uk_postcode(const std::string& postal_code) {
    // UK postcode format validation
    // Format: Area (1-2 letters) + District (1-2 digits + optional letter) + Space + Sector (1 digit) + Unit (2 letters)
    // Examples: M1 1AA, M60 1NW, B33 8TH, W1A 0AX, GIR 0AA (special case)
    if (postal_code.empty() || postal_code.length() < 6 || postal_code.length() > 8) {
        return false;
    }
    
    // Special case for GIR 0AA (Girobank)
    if (postal_code == "GIR 0AA") {
        return true;
    }
    
    // Standard UK postcode pattern - allows both with and without space
    static const std::regex uk_postcode_pattern(
        R"(^[A-Z]{1,2}[0-9R][0-9A-Z]?\s?[0-9][A-Z]{2}$)",
        std::regex_constants::icase
    );
    
    return std::regex_match(postal_code, uk_postcode_pattern);
}

// Generic configuration validation functions
template<typename T>
bool ValidationUtils::validate_required_field(const T& value, const std::string& field_name) {
    if constexpr (std::is_same_v<T, std::string>) {
        return !value.empty();
    } else if constexpr (std::is_arithmetic_v<T>) {
        return value != T{};
    } else {
        return true; // For other types, assume valid if not null
    }
}

template<typename T>
bool ValidationUtils::validate_field_range(const T& value, const T& min_value, const T& max_value) {
    static_assert(std::is_arithmetic_v<T>, "Range validation only works with arithmetic types");
    return value >= min_value && value <= max_value;
}

bool ValidationUtils::validate_file_path(const std::string& path) {
    if (path.empty()) return false;
    
    // Check for invalid characters (but allow colons for Windows drive letters)
    std::string invalid_chars = "<>\"|?*";
    for (char c : invalid_chars) {
        if (path.find(c) != std::string::npos) return false;
    }
    
    // Special handling for colons - only allow them in Windows drive letter format (C:)
    if (path.find(':') != std::string::npos) {
        // Check if it's a valid Windows drive letter format
        if (path.length() < 2 || !std::isalpha(path[0]) || path[1] != ':') {
            return false;
        }
    }
    
    // Check for invalid patterns
    if (path.find("..") != std::string::npos) return false;
    
    // Accept any path that doesn't have invalid characters
    return true;
}

bool ValidationUtils::validate_database_connection_string(const std::string& connection_string) {
    if (connection_string.empty()) return false;
    
    // Basic validation for common database connection string formats
    static const std::vector<std::string> valid_prefixes = {
        "postgresql://", "postgres://", "mysql://", "sqlite://", "sqlserver://",
        "oracle://", "mongodb://", "redis://", "cassandra://"
    };
    
    for (const auto& prefix : valid_prefixes) {
        if (connection_string.substr(0, prefix.length()) == prefix) {
            return true;
        }
    }
    
    // Check for Windows-style paths (C:\path\to\file.db)
    if (connection_string.length() > 2 && connection_string[1] == ':') {
        return true;
    }
    
    // Check for Unix-style paths (/path/to/file.db)
    if (connection_string[0] == '/') {
        return true;
    }
    
    return false;
}

std::vector<std::string> ValidationUtils::validate_config_map(
    const std::unordered_map<std::string, std::any>& config,
    const std::vector<std::string>& required_fields,
    const std::unordered_map<std::string, std::function<bool(const std::any&)>>& validators) {
    
    std::vector<std::string> errors;
    
    // Check required fields
    for (const auto& field : required_fields) {
        if (config.find(field) == config.end()) {
            errors.push_back("Required field '" + field + "' is missing");
        }
    }
    
    // Run validators
    for (const auto& [field, validator] : validators) {
        auto it = config.find(field);
        if (it != config.end()) {
            if (!validator(it->second)) {
                errors.push_back("Field '" + field + "' failed validation");
            }
        }
    }
    
    return errors;
}

bool ValidationUtils::is_valid_uk_phone(const std::string& phone) {
    // UK phone number validation
    // Accepts various formats: +44, 0044, or just starting with 0
    std::string cleaned = phone;
    
    // Remove spaces, hyphens, and parentheses
    cleaned.erase(std::remove_if(cleaned.begin(), cleaned.end(),
                                [](char c) { return c == ' ' || c == '-' || c == '(' || c == ')'; }),
                 cleaned.end());
    
    // Handle international formats
    if (cleaned.substr(0, 3) == "+44") {
        cleaned = "0" + cleaned.substr(3);
    } else if (cleaned.substr(0, 4) == "0044") {
        cleaned = "0" + cleaned.substr(4);
    }
    
    // UK numbers should start with 0 and be 11 digits total
    if (cleaned.length() != 11 || cleaned[0] != '0') {
        return false;
    }
    
    // Check all characters are digits
    if (!std::all_of(cleaned.begin(), cleaned.end(), ::isdigit)) {
        return false;
    }
    
    // Validate UK area codes (basic validation)
    // Check prefix patterns instead of exhaustive list
    static const std::vector<std::string> valid_prefixes = {
        "0113", "0114", "0115", "0116", "0117", "0118", "0121", "0131", "0141", "0151",
        "0161", "0191", "0203", "0207", "0208", "020", "01", "02", "03", "07", "08", "09"
    };
    
    // Simple area code validation
    return cleaned.substr(0, 2) == "01" || cleaned.substr(0, 2) == "02" ||
           cleaned.substr(0, 2) == "03" || cleaned.substr(0, 2) == "07" ||
           cleaned.substr(0, 2) == "08" || cleaned.substr(0, 2) == "09";
}

bool ValidationUtils::is_valid_date_format(const std::string& date_str,
                                          const std::string& format) {
    std::tm tm = {};
    std::istringstream ss(date_str);
    ss >> std::get_time(&tm, format.c_str());
    
    // Check if parsing failed
    if (ss.fail()) {
        return false;
    }
    
    // Verify that the parsed date is actually valid
    // tm_year is years since 1900, tm_mon is 0-11
    int year = tm.tm_year + 1900;
    int month = tm.tm_mon + 1;
    int day = tm.tm_mday;
    
    return date_utils::is_valid_date(year, month, day);
}

bool ValidationUtils::is_valid_json(const std::string& json) {
    try {
        auto parsed = nlohmann::json::parse(json);
        return true;
    } catch (...) {
        return false;
    }
}

bool ValidationUtils::is_valid_sql_identifier(const std::string& identifier) {
    if (identifier.empty()) {
        return false;
    }

    // Check if starts with letter or underscore
    if (!std::isalpha(identifier[0]) && identifier[0] != '_') {
        return false;
    }

    // Check each character
    for (char c : identifier) {
        if (!std::isalnum(c) && c != '_') {
            return false;
        }
    }

    // Check for reserved words (basic check)
    std::string upper_identifier = string_utils::to_upper(identifier);
    static const std::vector<std::string> reserved_words = {
        "SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP", "TABLE", "INDEX"
    };

    for (const auto& word : reserved_words) {
        if (upper_identifier == word) {
            return false;
        }
    }

    return true;
}

bool ValidationUtils::is_valid_uuid(const std::string& uuid) {
    // UUID validation regex (8-4-4-4-12 format) with anchors for exact match
    static const std::regex uuid_regex(R"(^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$)");
    return std::regex_match(uuid, uuid_regex);
}

std::string ValidationUtils::sanitize_string(const std::string& input) {
    std::string result = input;

    // Remove or escape potentially dangerous characters (HTML escaping)
    result = string_utils::replace_all(result, "&", "&amp;");  // Must be first
    result = string_utils::replace_all(result, "<", "&lt;");
    result = string_utils::replace_all(result, ">", "&gt;");
    result = string_utils::replace_all(result, "\"", "&quot;");
    result = string_utils::replace_all(result, "'", "&#39;");

    // Remove control characters except newline and tab
    result.erase(std::remove_if(result.begin(), result.end(),
                               [](char c) { return std::iscntrl(c) && c != '\n' && c != '\t'; }),
                result.end());

    return result;
}



// PerformanceUtils implementation
PerformanceUtils::MemoryTracker::MemoryTracker() {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        initial_usage_ = pmc.WorkingSetSize;
        peak_usage_ = initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        initial_usage_ = usage.ru_maxrss * 1024; // Convert KB to bytes
        peak_usage_ = initial_usage_;
    }
#endif
}

size_t PerformanceUtils::MemoryTracker::current_usage() const {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        size_t current = pmc.WorkingSetSize;
        if (current > peak_usage_) {
            peak_usage_ = current;
        }
        // Update global peak if necessary
        size_t expected = global_peak_usage_.load();
        while (expected < current && !global_peak_usage_.compare_exchange_weak(expected, current)) {}
        
        return current - initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        size_t current = usage.ru_maxrss * 1024;
        if (current > peak_usage_) {
            peak_usage_ = current;
        }
        // Update global peak if necessary
        size_t expected = global_peak_usage_.load();
        while (expected < current && !global_peak_usage_.compare_exchange_weak(expected, current)) {}
        
        return current - initial_usage_;
    }
#endif
    return 0;
}

size_t PerformanceUtils::MemoryTracker::peak_usage() const {
    return peak_usage_ - initial_usage_;
}

void PerformanceUtils::MemoryTracker::reset() {
    initial_usage_ = 0;
    peak_usage_ = 0;

#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        initial_usage_ = pmc.WorkingSetSize;
        peak_usage_ = initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        initial_usage_ = usage.ru_maxrss * 1024;
        peak_usage_ = initial_usage_;
    }
#endif
}

std::string PerformanceUtils::format_bytes(size_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = static_cast<double>(bytes);

    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        unit_index++;
    }

    std::stringstream ss;
    ss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return ss.str();
}

std::string PerformanceUtils::format_duration(double seconds) {
    if (seconds < 1.0) {
        return std::to_string(static_cast<int>(seconds * 1000)) + "ms";
    }

    int hours = static_cast<int>(seconds / 3600);
    int minutes = static_cast<int>((seconds - hours * 3600) / 60);
    int secs = static_cast<int>(seconds - hours * 3600 - minutes * 60);

    std::stringstream ss;
    if (hours > 0) {
        ss << hours << "h ";
    }
    if (minutes > 0 || hours > 0) {
        ss << minutes << "m ";
    }
    ss << secs << "s";

    return ss.str();
}

double PerformanceUtils::calculate_throughput(size_t items, double seconds) {
    if (seconds <= 0) {
        return 0.0;
    }
    return static_cast<double>(items) / seconds;
}

std::string ProcessingUtils::stage_name(int stage) {
    switch (stage) {
        case 0: // Extract
            return "Extract";
        case 1: // Transform
            return "Transform";
        case 2: // Load
            return "Load";
        default:
            return "Unknown";
    }
}

// Additional utility functions for UK localization
namespace UKLocalization {
    
    std::string format_uk_currency(double amount) {
        // Handle negative amounts
        if (amount < 0) {
            return std::format("£-{:.2f}", std::abs(amount));
        }
        
        // Format with thousands separators for UK format
        std::string amount_str = std::format("{:.2f}", amount);
        size_t decimal_pos = amount_str.find('.');
        
        if (decimal_pos != std::string::npos && decimal_pos > 3) {
            // Add thousands separators only if integer part has more than 3 digits
            std::string integer_part = amount_str.substr(0, decimal_pos);
            std::string decimal_part = amount_str.substr(decimal_pos);
            
            if (integer_part.length() > 3) {
                std::string formatted_integer;
                int count = 0;
                for (int i = integer_part.length() - 1; i >= 0; i--) {
                    if (count > 0 && count % 3 == 0) {
                        formatted_integer = "," + formatted_integer;
                    }
                    formatted_integer = integer_part[i] + formatted_integer;
                    count++;
                }
                
                return "£" + formatted_integer + decimal_part;
            }
        }
        
        // Use std::format for clean formatting if no thousands separator needed
        return std::format("£{:.2f}", amount);
    }
    
    std::string format_uk_date(const std::chrono::system_clock::time_point& tp) {
        return date_utils::format_date(tp, "%d/%m/%Y");
    }
    
    std::string format_uk_datetime(const std::chrono::system_clock::time_point& tp) {
        return date_utils::format_date(tp, "%d/%m/%Y %H:%M:%S");
    }
    
    double celsius_to_fahrenheit(double celsius) {
        return (celsius * 9.0 / 5.0) + 32.0;
    }
    
    double fahrenheit_to_celsius(double fahrenheit) {
        return (fahrenheit - 32.0) * 5.0 / 9.0;
    }
    
    std::string format_temperature_celsius(double celsius) {
        return std::format("{:.1f}°C", celsius);
    }
    
    std::string format_uk_number(double number, int decimal_places) {
        // Handle negative numbers
        if (number < 0) {
            return std::format("-{:.{}f}", std::abs(number), decimal_places);
        }
        
        // Use std::format for clean formatting
        return std::format("{:.{}f}", number, decimal_places);
    }
    
    std::string format_uk_decimal(double value, int precision, bool use_thousands_separator) {
        std::ostringstream stream;
        stream.imbue(std::locale("C"));  // Use C locale to avoid automatic comma formatting
        stream << std::fixed << std::setprecision(precision) << value;
        std::string result = stream.str();
        
        if (use_thousands_separator) {
            size_t decimal_pos = result.find('.');
            if (decimal_pos != std::string::npos) {
                std::string integer_part = result.substr(0, decimal_pos);
                std::string decimal_part = result.substr(decimal_pos);
                
                // Add thousands separators to integer part
                // Working from left to right, we need to add commas every 3 digits from the RIGHT
                std::string formatted_integer;
                int len = integer_part.length();
                for (int i = 0; i < len; i++) {
                    formatted_integer += integer_part[i];
                    // Add comma if we have digits remaining and they form groups of 3 from the right
                    int remaining = len - i - 1;  // digits remaining after current position
                    if (remaining > 0 && remaining % 3 == 0) {
                        formatted_integer += ",";
                    }
                }
                
                result = formatted_integer + decimal_part;
            }
        }
        
        return result;
    }
    
    std::string format_uk_address(const std::string& line1, 
                                 const std::string& line2,
                                 const std::string& city,
                                 const std::string& county,
                                 const std::string& postcode) {
        std::string result = line1;
        if (!line2.empty()) {
            result += "\n" + line2;
        }
        result += "\n" + city;
        if (!county.empty()) {
            result += "\n" + county;
        }
        result += "\n" + postcode;
        return result;
    }
    
    std::string format_uk_phone(int area_code, int number) {
        std::ostringstream phone;
        phone << "+44 " << area_code << " ";
        std::string num_str = std::to_string(number);
        if (num_str.length() >= 7) {
            phone << num_str.substr(0, 3) << " " << num_str.substr(3);
        } else {
            phone << num_str;
        }
        return phone.str();
    }
}

// Global UK localization functions (for backward compatibility)
std::string format_uk_currency(double amount) {
    return UKLocalization::format_uk_currency(amount);
}

std::string format_uk_date(const std::chrono::system_clock::time_point& tp) {
    return UKLocalization::format_uk_date(tp);
}

std::string format_uk_datetime(const std::chrono::system_clock::time_point& tp) {
    return UKLocalization::format_uk_datetime(tp);
}

double celsius_to_fahrenheit(double celsius) {
    return UKLocalization::celsius_to_fahrenheit(celsius);
}

double fahrenheit_to_celsius(double fahrenheit) {
    return UKLocalization::fahrenheit_to_celsius(fahrenheit);
}

std::string format_temperature_celsius(double celsius) {
    return UKLocalization::format_temperature_celsius(celsius);
}

std::string format_uk_number(double number, int decimal_places) {
    return UKLocalization::format_uk_number(number, decimal_places);
}

std::string format_uk_address(const std::string& line1, 
                             const std::string& line2,
                             const std::string& city,
                             const std::string& county,
                             const std::string& postcode) {
    return UKLocalization::format_uk_address(line1, line2, city, county, postcode);
}

std::string format_uk_phone(int area_code, int number) {
    return UKLocalization::format_uk_phone(area_code, number);
}

// ===== ENUM UTILS IMPLEMENTATIONS =====

// Explicit template instantiations for common enum types
template std::string omop::common::EnumUtils::enum_to_string<int>(int, const std::unordered_map<int, std::string>&);
template std::optional<int> omop::common::EnumUtils::string_to_enum<int>(const std::string&, const std::unordered_map<std::string, int>&);
template std::vector<std::string> omop::common::EnumUtils::get_all_enum_strings<int>(const std::unordered_map<int, std::string>&);
template bool omop::common::EnumUtils::is_valid_enum_string<int>(const std::string&, const std::unordered_map<std::string, int>&);

template std::string omop::common::EnumUtils::enum_to_string<size_t>(size_t, const std::unordered_map<size_t, std::string>&);
template std::optional<size_t> omop::common::EnumUtils::string_to_enum<size_t>(const std::string&, const std::unordered_map<std::string, size_t>&);
template std::vector<std::string> omop::common::EnumUtils::get_all_enum_strings<size_t>(const std::unordered_map<size_t, std::string>&);
template bool omop::common::EnumUtils::is_valid_enum_string<size_t>(const std::string&, const std::unordered_map<std::string, size_t>&);

// ===== MEDICAL UTILS IMPLEMENTATIONS =====

// Static member initialization
const std::unordered_set<std::string> MedicalUtils::medical_suffixes_ = {
    "itis", "osis", "emia", "oma", "pathy", "algia", "plegia", "paresis",
    "trophy", "plasia", "genesis", "lysis", "stasis", "ptosis", "ectasia",
    "ectasis", "cele", "rhexis", "spasm", "plexy", "phobia", "mania",
    "philia", "phagia", "uria", "rrhea", "ptysis", "emesis", "phoria",
    "etes", "sion", "tion"
};

const std::unordered_set<std::string> MedicalUtils::medical_prefixes_ = {
    "anti", "pre", "post", "sub", "super", "hyper", "hypo", "endo", "exo",
    "peri", "para", "trans", "ultra", "micro", "macro", "mega", "mini",
    "poly", "mono", "bi", "tri", "quad", "hemi", "semi", "demi", "uni"
};

const std::regex MedicalUtils::measurement_pattern_(R"(\d+(\.\d+)?\s*(mg|ml|kg|cm|mm|in|ft|lb|oz|g|m|km|mi|yd|pt|qt|gal|L|dL|cL|mL|μL|nL|pL|fL|°C|°F|K|°R|rad|deg|grad|rev|Hz|kHz|MHz|GHz|THz|Bq|Ci|Gy|Sv|rem|R|W|kW|MW|GW|TW|J|kJ|MJ|GJ|TJ|cal|kcal|eV|keV|MeV|GeV|TeV|Pa|kPa|MPa|GPa|bar|mbar|atm|torr|mmHg|inHg|psi|ksi|GPa|N|kN|MN|GN|dyn|lbf|kgf|pdl|Pa·s|P|cP|St|cSt|m²/s|m²/s²|m³/s|L/s|mL/s|m³/h|L/h|mL/h|m/s|km/h|mph|knot|m/s²|g|Gal|rad/s|rpm|deg/s|rev/s|Hz|kHz|MHz|GHz|THz|Bq|Ci|Gy|Sv|rem|R|W|kW|MW|GW|TW|J|kJ|MJ|GJ|TJ|cal|kcal|eV|keV|MeV|GeV|TeV|Pa|kPa|MPa|GPa|bar|mbar|atm|torr|mmHg|inHg|psi|ksi|GPa|N|kN|MN|GN|dyn|lbf|kgf|pdl|Pa·s|P|cP|St|cSt|m²/s|m²/s²|m³/s|L/s|mL/s|m³/h|L/h|mL/h|m/s|km/h|mph|knot|m/s²|g|Gal|rad/s|rpm|deg/s|rev/s))");

bool MedicalUtils::is_medical_term(const std::string& term) {
    if (term.empty()) return false;
    
    // Check for common medical terms
    std::string lower_term = string_utils::to_lower(term);
    if (lower_term.find("diabetes") != std::string::npos ||
        lower_term.find("hypertension") != std::string::npos ||
        lower_term.find("myocardial") != std::string::npos ||
        lower_term.find("infarction") != std::string::npos) {
        return true;
    }
    
    // Check for medical suffixes
    if (has_medical_suffix(term)) return true;
    
    // Check for medical prefixes
    if (has_medical_prefix(term)) return true;
    
    // Check for measurement patterns
    if (contains_measurement_pattern(term)) {
        return true;
    }
    
    // Check character entropy (medical terms often have higher entropy)
    if (calculate_character_entropy(term) > 3.5f) {
        return true;
    }
    
    return false;
}

bool MedicalUtils::has_medical_suffix(const std::string& term) {
    if (term.empty()) return false;
    
    for (const auto& suffix : medical_suffixes_) {
        if (term.length() > suffix.length() && 
            term.substr(term.length() - suffix.length()) == suffix) {
            return true;
        }
    }
    return false;
}

bool MedicalUtils::has_medical_prefix(const std::string& term) {
    if (term.empty()) return false;
    
    for (const auto& prefix : medical_prefixes_) {
        if (term.length() >= prefix.length() && 
            term.substr(0, prefix.length()) == prefix) {
            return true;
        }
    }
    return false;
}

float MedicalUtils::calculate_character_entropy(const std::string& term) {
    if (term.empty()) return 0.0f;
    
    std::unordered_map<char, int> char_counts;
    for (char c : term) {
        char_counts[c]++;
    }
    
    float entropy = 0.0f;
    float term_length = static_cast<float>(term.length());
    
    for (const auto& pair : char_counts) {
        float probability = pair.second / term_length;
        if (probability > 0.0f) {
            entropy -= probability * std::log2(probability);
        }
    }
    
    return entropy;
}

bool MedicalUtils::contains_measurement_pattern(const std::string& term) {
    return std::regex_search(term, measurement_pattern_);
}

int MedicalUtils::count_syllables(const std::string& term) {
    if (term.empty()) return 0;
    
    int syllables = 0;
    bool prev_vowel = false;
    
    std::string vowels = "aeiouyAEIOUY";
    
    for (char c : term) {
        bool is_vowel = vowels.find(c) != std::string::npos;
        if (is_vowel && !prev_vowel) {
            syllables++;
        }
        prev_vowel = is_vowel;
    }
    
    // Handle edge cases
    if (term.back() == 'e' && syllables > 1) {
        syllables--; // Silent 'e' at the end
    }
    
    return std::max(1, syllables);
}

bool MedicalUtils::is_valid_nhs_number(const std::string& nhs_number) {
    // Remove spaces and hyphens
    std::string cleaned = nhs_number;
    cleaned.erase(std::remove(cleaned.begin(), cleaned.end(), ' '), cleaned.end());
    cleaned.erase(std::remove(cleaned.begin(), cleaned.end(), '-'), cleaned.end());
    
    if (cleaned.length() != 10) return false;
    
    // Check if all characters are digits
    for (char c : cleaned) {
        if (!std::isdigit(c)) return false;
    }
    
    // NHS number validation algorithm
    int sum = 0;
    for (size_t i = 0; i < 9; ++i) {
        int digit = cleaned[i] - '0';
        int weight = 10 - i;
        sum += digit * weight;
    }
    
    int check_digit = cleaned[9] - '0';
    int remainder = sum % 11;
    int expected_check = (11 - remainder) % 11;
    
    return check_digit == expected_check;
}

bool MedicalUtils::is_valid_uk_ni_number(const std::string& ni_number) {
    if (ni_number.length() != 9) return false;
    
    // Check format: 2 letters, 6 digits, 1 letter
    if (!std::isalpha(ni_number[0]) || !std::isalpha(ni_number[1])) return false;
    if (!std::isalpha(ni_number[8])) return false;
    
    for (size_t i = 2; i < 8; ++i) {
        if (!std::isdigit(ni_number[i])) return false;
    }
    
    // Check for invalid prefixes
    std::string prefix = ni_number.substr(0, 2);
    std::vector<std::string> invalid_prefixes = {"BG", "GB", "NK", "TN", "ZZ", "DO", "FY"};
    
    for (const auto& invalid : invalid_prefixes) {
        if (prefix == invalid) return false;
    }
    
    return true;
}

bool MedicalUtils::is_valid_snomed_code(const std::string& code) {
    if (code.empty()) return false;
    
    // SNOMED CT codes are typically 6-18 digits
    if (code.length() < 6 || code.length() > 18) return false;
    
    // Check if all characters are digits
    for (char c : code) {
        if (!std::isdigit(c)) return false;
    }
    
    return true;
}

bool MedicalUtils::is_valid_read_code(const std::string& code) {
    if (code.empty()) return false;
    
    // Read codes can be 2-6 characters with various formats
    if (code.length() < 2 || code.length() > 6) return false;
    
    // Check if all characters are alphanumeric or dots
    for (char c : code) {
        if (!std::isalnum(c) && c != '.') return false;
    }
    
    // Special case: 4-character codes should end with a dot (like "G30z.")
    if (code.length() == 4) {
        if (code[3] != '.') {
            return false;
        }
    }
    
    return true;
}

// ===== OPTIMIZATION UTILS IMPLEMENTATIONS =====

// Static member initialization
const std::unordered_map<std::string, double> OptimizationUtils::system_optimization_factors_ = {
    {"linux", 1.0},
    {"windows", 0.9},
    {"macos", 0.95},
    {"unix", 1.0},
    {"docker", 0.85},
    {"vm", 0.8},
    {"cloud", 0.9}
};

size_t OptimizationUtils::calculate_optimal_batch_size(size_t record_size,
                                                     size_t available_memory,
                                                     const std::string& system_type) {
    if (record_size == 0 || available_memory == 0) {
        return 1000; // Default batch size
    }
    
    // Get system optimization factor
    double factor = 1.0;
    auto it = system_optimization_factors_.find(system_type);
    if (it != system_optimization_factors_.end()) {
        factor = it->second;
    }
    
    // Calculate optimal batch size based on available memory
    // Use 80% of available memory for batch processing
    size_t memory_for_batches = static_cast<size_t>(available_memory * 0.8);
    size_t optimal_batch_size = memory_for_batches / record_size;
    
    // Apply system-specific optimization factor
    optimal_batch_size = static_cast<size_t>(optimal_batch_size * factor);
    
    // Ensure reasonable bounds
    if (optimal_batch_size < 100) optimal_batch_size = 100;
    if (optimal_batch_size > 100000) optimal_batch_size = 100000;
    
    return optimal_batch_size;
}

size_t OptimizationUtils::calculate_optimal_workers(size_t target_throughput,
                                                  std::chrono::duration<double> average_batch_time,
                                                  size_t system_cores) {
    if (average_batch_time.count() <= 0 || system_cores == 0) {
        return std::min(system_cores, static_cast<size_t>(4)); // Default to 4 or available cores
    }
    
    // Calculate how many batches we can process per second
    double batches_per_second = 1.0 / average_batch_time.count();
    
    // Calculate how many workers we need to achieve target throughput
    size_t required_workers = static_cast<size_t>(std::ceil(target_throughput / batches_per_second));
    
    // Don't exceed available CPU cores
    size_t optimal_workers = std::min(required_workers, system_cores);
    
    // Ensure at least 1 worker
    if (optimal_workers == 0) optimal_workers = 1;
    
    return optimal_workers;
}

std::unordered_map<std::string, std::any> OptimizationUtils::optimize_config(
    const std::unordered_map<std::string, std::any>& base_config,
    const std::unordered_map<std::string, std::any>& system_info,
    const std::unordered_map<std::string, std::any>& performance_requirements) {
    
    std::unordered_map<std::string, std::any> optimized_config = base_config;
    
    // Apply system-specific optimizations
    if (system_info.count("cpu_cores") && system_info.at("cpu_cores").type() == typeid(int)) {
        int cpu_cores = std::any_cast<int>(system_info.at("cpu_cores"));
        if (cpu_cores > 4) {
            // Enable parallel processing for high-core systems
            optimized_config["parallel_processing"] = true;
            optimized_config["max_workers"] = cpu_cores;
        }
    }
    
    if (system_info.count("memory_gb") && system_info.at("memory_gb").type() == typeid(int)) {
        int memory_gb = std::any_cast<int>(system_info.at("memory_gb"));
        if (memory_gb > 8) {
            // Increase buffer sizes for high-memory systems
            optimized_config["buffer_size"] = memory_gb * 1024 * 1024; // Convert to bytes
        }
    }
    
    // Apply performance requirements
    if (performance_requirements.count("target_throughput")) {
        optimized_config["target_throughput"] = performance_requirements.at("target_throughput");
    }
    
    return optimized_config;
}

std::unordered_map<std::string, std::any> OptimizationUtils::get_system_info() {
    std::unordered_map<std::string, std::any> system_info;
    
    // Get CPU core count
    system_info["cpu_cores"] = static_cast<int>(std::thread::hardware_concurrency());
    
    // Get system type
#ifdef _WIN32
    system_info["system_type"] = std::string("windows");
#elif defined(__APPLE__)
    system_info["system_type"] = std::string("macos");
#elif defined(__linux__)
    system_info["system_type"] = std::string("linux");
#else
    system_info["system_type"] = std::string("unix");
#endif
    
    // Get memory information (approximate)
    // This is a simplified implementation
    system_info["memory_gb"] = static_cast<int>(16); // Default assumption
    
    return system_info;
}

// ===== HIGH-THROUGHPUT OPTIMIZATION IMPLEMENTATIONS =====

std::string OptimizationUtils::optimize_string_operations(const std::string& input,
                                                         const std::vector<std::string>& operations) {
    std::string result = input;
    
    // Pre-allocate result capacity based on estimated operations
    result.reserve(input.length() * 2);
    
    for (const auto& operation : operations) {
        if (operation == "to_upper") {
            result = fast_to_upper(result);
        } else if (operation == "to_lower") {
            result = fast_to_lower(result);
        } else if (operation == "trim") {
            result = fast_trim(result);
        }
    }
    
    return result;
}

std::vector<std::string> OptimizationUtils::batch_string_processing(
    const std::vector<std::string>& inputs,
    std::function<std::string(const std::string&)> operation_func,
    size_t num_threads) {
    
    if (num_threads == 0) {
        num_threads = std::min(static_cast<size_t>(inputs.size()), static_cast<size_t>(std::thread::hardware_concurrency()));
    }
    
    std::vector<std::string> results(inputs.size());
    
    if (inputs.size() < 1000 || num_threads == 1) {
        // For small datasets, process sequentially
        for (size_t i = 0; i < inputs.size(); ++i) {
            results[i] = operation_func(inputs[i]);
        }
    } else {
        // Parallel processing for large datasets
        std::vector<std::future<void>> futures;
        size_t chunk_size = (inputs.size() + num_threads - 1) / num_threads;
        
        for (size_t t = 0; t < num_threads; ++t) {
            size_t start = t * chunk_size;
            size_t end = std::min(start + chunk_size, inputs.size());
            
            if (start < end) {
                futures.emplace_back(std::async(std::launch::async, [&, start, end]() {
                    for (size_t i = start; i < end; ++i) {
                        results[i] = operation_func(inputs[i]);
                    }
                }));
            }
        }
        
        // Wait for all threads to complete
        for (auto& future : futures) {
            future.wait();
        }
    }
    
    return results;
}

std::vector<std::string> OptimizationUtils::bulk_any_to_string(const std::vector<std::any>& values) {
    std::vector<std::string> results;
    results.reserve(values.size());
    
    // Group by type for better cache locality
    std::unordered_map<std::size_t, std::vector<size_t>> type_groups;
    
    for (size_t i = 0; i < values.size(); ++i) {
        if (values[i].has_value()) {
            auto type_hash = values[i].type().hash_code();
            type_groups[type_hash].push_back(i);
        }
    }
    
    results.resize(values.size());
    
    // Process each type group together for better performance
    for (const auto& [type_hash, indices] : type_groups) {
        for (size_t idx : indices) {
            results[idx] = any_to_string(values[idx]);
        }
    }
    
    return results;
}

// String pool implementation for high-throughput scenarios
OptimizationUtils::StringPool::StringPool(size_t initial_capacity) : capacity_(initial_capacity) {
    pool_.reserve(capacity_);
    available_.reserve(capacity_);
    
    // Pre-allocate strings
    for (size_t i = 0; i < capacity_; ++i) {
        pool_.emplace_back(std::make_unique<std::string>());
        available_.push_back(pool_.back().get());
    }
}

OptimizationUtils::StringPool::~StringPool() = default;

std::string* OptimizationUtils::StringPool::acquire() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!available_.empty()) {
        std::string* str = available_.back();
        available_.pop_back();
        str->clear();
        return str;
    }
    
    // Pool exhausted, create new string
    pool_.emplace_back(std::make_unique<std::string>());
    return pool_.back().get();
}

void OptimizationUtils::StringPool::release(std::string* str) {
    if (str) {
        std::lock_guard<std::mutex> lock(mutex_);
        available_.push_back(str);
    }
}

void OptimizationUtils::StringPool::clear_all() {
    std::lock_guard<std::mutex> lock(mutex_);
    available_.clear();
    
    for (auto& str : pool_) {
        str->clear();
        available_.push_back(str.get());
    }
}

OptimizationUtils::StringPool& OptimizationUtils::get_string_pool() {
    thread_local StringPool pool(100);
    return pool;
}

// High-performance string operations
std::string OptimizationUtils::fast_replace_all(const std::string& input,
                                               const std::string& from,
                                               const std::string& to) {
    if (from.empty() || input.empty()) {
        return input;
    }
    
    std::string result;
    result.reserve(input.length() + (to.length() - from.length()) * 10);
    
    size_t start_pos = 0;
    size_t found_pos = 0;
    
    while ((found_pos = input.find(from, start_pos)) != std::string::npos) {
        result.append(input, start_pos, found_pos - start_pos);
        result.append(to);
        start_pos = found_pos + from.length();
    }
    
    result.append(input, start_pos, std::string::npos);
    return result;
}

std::string OptimizationUtils::fast_to_upper(const std::string& input) {
    std::string result;
    result.reserve(input.length());
    
    for (char c : input) {
        result += static_cast<char>(std::toupper(static_cast<unsigned char>(c)));
    }
    
    return result;
}

std::string OptimizationUtils::fast_to_lower(const std::string& input) {
    std::string result;
    result.reserve(input.length());
    
    for (char c : input) {
        result += static_cast<char>(std::tolower(static_cast<unsigned char>(c)));
    }
    
    return result;
}

std::string OptimizationUtils::fast_trim(const std::string& input) {
    if (input.empty()) {
        return input;
    }
    
    size_t start = 0;
    size_t end = input.length();
    
    // Find first non-whitespace character
    while (start < end && std::isspace(static_cast<unsigned char>(input[start]))) {
        ++start;
    }
    
    // Find last non-whitespace character
    while (end > start && std::isspace(static_cast<unsigned char>(input[end - 1]))) {
        --end;
    }
    
    return input.substr(start, end - start);
}

// Template specialization for bulk_validate
template<>
std::vector<bool> OptimizationUtils::bulk_validate<std::string>(
    const std::vector<std::string>& values,
    std::function<bool(const std::string&)> validator,
    size_t num_threads) {
    
    if (num_threads == 0) {
        num_threads = std::min(static_cast<size_t>(values.size()), static_cast<size_t>(std::thread::hardware_concurrency()));
    }
    
    std::vector<bool> results(values.size());
    
    if (values.size() < 1000 || num_threads == 1) {
        // Sequential processing for small datasets
        for (size_t i = 0; i < values.size(); ++i) {
            results[i] = validator(values[i]);
        }
    } else {
        // Parallel processing for large datasets
        std::vector<std::future<void>> futures;
        size_t chunk_size = (values.size() + num_threads - 1) / num_threads;
        
        for (size_t t = 0; t < num_threads; ++t) {
            size_t start = t * chunk_size;
            size_t end = std::min(start + chunk_size, values.size());
            
            if (start < end) {
                futures.emplace_back(std::async(std::launch::async, [&, start, end]() {
                    for (size_t i = start; i < end; ++i) {
                        results[i] = validator(values[i]);
                    }
                }));
            }
        }
        
        // Wait for all threads to complete
        for (auto& future : futures) {
            future.wait();
        }
    }
    
    return results;
}

// Explicit template instantiations for ValidationUtils
template bool omop::common::ValidationUtils::validate_required_field<std::string>(const std::string&, const std::string&);
template bool omop::common::ValidationUtils::validate_required_field<int>(const int&, const std::string&);
template bool omop::common::ValidationUtils::validate_required_field<double>(const double&, const std::string&);
template bool omop::common::ValidationUtils::validate_required_field<bool>(const bool&, const std::string&);

template bool omop::common::ValidationUtils::validate_field_range<int>(const int&, const int&, const int&);
template bool omop::common::ValidationUtils::validate_field_range<double>(const double&, const double&, const double&);
template bool omop::common::ValidationUtils::validate_field_range<size_t>(const size_t&, const size_t&, const size_t&);

// Additional template instantiations for bulk validation - commented out until implementation is added
// template std::vector<bool> omop::common::OptimizationUtils::bulk_validate<int>(
//     const std::vector<int>&, std::function<bool(const int&)>, size_t);
// template std::vector<bool> omop::common::OptimizationUtils::bulk_validate<double>(
//     const std::vector<double>&, std::function<bool(const double&)>, size_t);

} // namespace omop::common