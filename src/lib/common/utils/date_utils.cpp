/**
 * @file date_utils.cpp
 * @brief Implementation of date and time utility functions - refactored from DateTimeUtils
 * <AUTHOR> Cancer Data Engineering  
 * @date 2025-08-25
 */

#include "date_utils.h"
#include <chrono>
#include <iomanip>
#include <sstream>
#include <ctime>
#include <algorithm>
#include <mutex>
#include <vector>

namespace omop::common::date_utils {

// Thread-safe mutex for time conversion functions (from DateTimeUtils)
static std::mutex time_conversion_mutex;

// Type aliases for convenience
using time_point = std::chrono::system_clock::time_point;
using duration_type = std::chrono::milliseconds;

// ========== CORE COMPATIBILITY FUNCTIONS (DateTimeUtils API) ==========

std::optional<time_point> parse_date(const std::string& date_str, const std::string& format) {
    std::tm tm = {};
    std::istringstream ss(date_str);
    
    // Lock for thread-safe time parsing
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        ss >> std::get_time(&tm, format.c_str());
    }

    if (ss.fail()) {
        return std::nullopt;
    }

    // Use mutex for mktime which is not thread-safe
    time_t time_value;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        time_value = std::mktime(&tm);
    }
    
    if (time_value == -1) {
        return std::nullopt;
    }
    
    return std::chrono::system_clock::from_time_t(time_value);
}

std::optional<time_point> parse_date_multiple_formats(
    const std::string& date_str,
    const std::vector<std::string>& formats) {
    
    for (const auto& format : formats) {
        std::tm tm = {};
        std::istringstream ss(date_str);
        
        // Lock for thread-safe time parsing
        {
            std::lock_guard<std::mutex> lock(time_conversion_mutex);
            ss >> std::get_time(&tm, format.c_str());
        }

        if (!ss.fail()) {
            tm.tm_isdst = -1;  // Let mktime determine if DST is in effect
            time_t time_value;
            {
                std::lock_guard<std::mutex> lock(time_conversion_mutex);
                time_value = std::mktime(&tm);
            }
            
            if (time_value != static_cast<time_t>(-1)) {
                return std::chrono::system_clock::from_time_t(time_value);
            }
        }
    }
    
    // Try ISO 8601 fallback formats
    std::vector<std::string> iso_formats = {
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d",
        "%Y/%m/%d",
        "%d/%m/%Y",
        "%m/%d/%Y"
    };
    
    for (const auto& format : iso_formats) {
        std::tm tm = {};
        std::istringstream ss(date_str);
        
        // Lock for thread-safe time parsing
        {
            std::lock_guard<std::mutex> lock(time_conversion_mutex);
            ss >> std::get_time(&tm, format.c_str());
        }

        if (!ss.fail()) {
            tm.tm_isdst = -1;  // Let mktime determine if DST is in effect
            time_t time_value;
            {
                std::lock_guard<std::mutex> lock(time_conversion_mutex);
                time_value = std::mktime(&tm);
            }
            
            if (time_value != static_cast<time_t>(-1)) {
                return std::chrono::system_clock::from_time_t(time_value);
            }
        }
    }
    
    return std::nullopt;
}

std::string format_date(const time_point& tp, const std::string& format) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::stringstream ss;
    
    // Thread-safe localtime
    std::tm tm_result;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&tm_result, &time_t);
#else
        localtime_r(&time_t, &tm_result);
#endif
    }
    
    ss << std::put_time(&tm_result, format.c_str());
    return ss.str();
}

std::string current_timestamp(const std::string& format) {
    return format_date(std::chrono::system_clock::now(), format);
}

time_point add_duration(const time_point& tp, int days, int hours, int minutes) {
    auto duration = std::chrono::hours(days * 24) +
                   std::chrono::hours(hours) +
                   std::chrono::minutes(minutes);
    return tp + duration;
}

int calculate_age(const time_point& birth_date, const time_point& as_of_date) {
    auto birth_time_t = std::chrono::system_clock::to_time_t(birth_date);
    auto as_of_time_t = std::chrono::system_clock::to_time_t(as_of_date);

    std::tm birth_tm, as_of_tm;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&birth_tm, &birth_time_t);
        localtime_s(&as_of_tm, &as_of_time_t);
#else
        localtime_r(&birth_time_t, &birth_tm);
        localtime_r(&as_of_time_t, &as_of_tm);
#endif
    }

    int age = as_of_tm.tm_year - birth_tm.tm_year;

    if (as_of_tm.tm_mon < birth_tm.tm_mon ||
        (as_of_tm.tm_mon == birth_tm.tm_mon && as_of_tm.tm_mday < birth_tm.tm_mday)) {
        age--;
    }

    return age;
}

bool is_valid_date(int year, int month, int day) {
    if (month < 1 || month > 12) return false;
    if (day < 1) return false;

    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    int max_day = days_in_month[month - 1];

    // Check for leap year
    if (month == 2 && ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0)) {
        max_day = 29;
    }

    return day <= max_day;
}

int get_iso_week(const time_point& tp) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::tm tm, jan1;
    
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&tm, &time_t);
#else
        localtime_r(&time_t, &tm);
#endif
    }

    // Simplified ISO week calculation
    // In production, use a proper date library
    jan1 = tm;
    jan1.tm_mon = 0;
    jan1.tm_mday = 1;
    
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        std::mktime(&jan1);
    }

    int days_since_jan1 = tm.tm_yday;
    int jan1_weekday = jan1.tm_wday;

    // Adjust for ISO week (Monday = 1, Sunday = 7)
    if (jan1_weekday == 0) jan1_weekday = 7;

    int iso_week = (days_since_jan1 + jan1_weekday - 1) / 7 + 1;

    return iso_week;
}

time_point convert_timezone(const time_point& tp, const std::string& from_tz, const std::string& to_tz) {
    // Fallback: basic offset conversion for common UK timezones
    if (from_tz == "UTC" && to_tz == "Europe/London") {
        // UTC to BST/GMT conversion
        auto local_time = std::chrono::system_clock::to_time_t(tp);
        std::tm tm_utc{};
        
        {
            std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
            gmtime_s(&tm_utc, &local_time);
#else
            gmtime_r(&local_time, &tm_utc);
#endif
        }
        
        // Simple BST check (last Sunday in March to last Sunday in October)
        bool is_bst = false;
        if (tm_utc.tm_mon >= 2 && tm_utc.tm_mon <= 9) { // March to October
            if (tm_utc.tm_mon > 2 && tm_utc.tm_mon < 9) {
                is_bst = true; // Definitely BST
            } else {
                // Check for last Sunday rule (simplified)
                is_bst = (tm_utc.tm_mon == 2) ? (tm_utc.tm_mday >= 25) : (tm_utc.tm_mday < 25);
            }
        }
        
        if (is_bst) {
            return tp + std::chrono::hours(1); // BST = UTC + 1
        }
    } else if (from_tz == "Europe/London" && to_tz == "UTC") {
        // Similar reverse conversion
        auto local_time = std::chrono::system_clock::to_time_t(tp);
        std::tm tm_local{};
        
        {
            std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
            localtime_s(&tm_local, &local_time);
#else
            localtime_r(&local_time, &tm_local);
#endif
        }
        
        // Simplified BST detection
        bool is_bst = false;
        if (tm_local.tm_mon >= 2 && tm_local.tm_mon <= 9) {
            if (tm_local.tm_mon > 2 && tm_local.tm_mon < 9) {
                is_bst = true;
            } else {
                is_bst = (tm_local.tm_mon == 2) ? (tm_local.tm_mday >= 25) : (tm_local.tm_mday < 25);
            }
        }
        
        if (is_bst) {
            return tp - std::chrono::hours(1); // UTC = BST - 1
        }
    }
    
    return tp; // Return original time if conversion fails
}

duration_type duration_between(const time_point& start, const time_point& end) {
    return std::chrono::duration_cast<duration_type>(end - start);
}

std::string format_duration_human(const duration_type& duration) {
    auto total_seconds = std::chrono::duration_cast<std::chrono::seconds>(duration).count();
    
    if (total_seconds < 60) {
        return std::to_string(total_seconds) + "s";
    }
    
    auto minutes = total_seconds / 60;
    auto seconds = total_seconds % 60;
    
    if (minutes < 60) {
        return std::to_string(minutes) + "m " + std::to_string(seconds) + "s";
    }
    
    auto hours = minutes / 60;
    minutes = minutes % 60;
    
    if (hours < 24) {
        return std::to_string(hours) + "h " + std::to_string(minutes) + "m";
    }
    
    auto days = hours / 24;
    hours = hours % 24;
    
    return std::to_string(days) + "d " + std::to_string(hours) + "h";
}

bool is_weekend(const time_point& tp) {
    auto time_t_value = std::chrono::system_clock::to_time_t(tp);
    std::tm tm;
    
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&tm, &time_t_value);
#else
        localtime_r(&time_t_value, &tm);
#endif
    }
    
    return tm.tm_wday == 0 || tm.tm_wday == 6;  // Sunday = 0, Saturday = 6
}

time_point start_of_day(const time_point& tp) {
    auto time_t_value = std::chrono::system_clock::to_time_t(tp);
    std::tm tm;
    
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&tm, &time_t_value);
#else
        localtime_r(&time_t_value, &tm);
#endif
    }
    
    tm.tm_hour = 0;
    tm.tm_min = 0;
    tm.tm_sec = 0;
    
    time_t start_time;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        start_time = std::mktime(&tm);
    }
    
    return std::chrono::system_clock::from_time_t(start_time);
}

time_point end_of_day(const time_point& tp) {
    auto time_t_value = std::chrono::system_clock::to_time_t(tp);
    std::tm tm;
    
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
#ifdef _WIN32
        localtime_s(&tm, &time_t_value);
#else
        localtime_r(&time_t_value, &tm);
#endif
    }
    
    tm.tm_hour = 23;
    tm.tm_min = 59;
    tm.tm_sec = 59;
    
    time_t end_time;
    {
        std::lock_guard<std::mutex> lock(time_conversion_mutex);
        end_time = std::mktime(&tm);
    }
    
    return std::chrono::system_clock::from_time_t(end_time);
}

// UK-specific formatting functions (from original DateTimeUtils)
std::string format_uk_date(const time_point& tp) {
    return format_date(tp, "%d/%m/%Y");
}

std::string format_uk_datetime(const time_point& tp) {
    return format_date(tp, "%d/%m/%Y %H:%M:%S");
}

std::string format_iso_date(const time_point& tp) {
    return format_date(tp, "%Y-%m-%d");
}

std::string format_iso_datetime(const time_point& tp) {
    return format_date(tp, "%Y-%m-%dT%H:%M:%S");
}

} // namespace omop::common::date_utils