/**
 * @file date_utils.h
 * @brief Optimized date and time utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <string_view>
#include <chrono>
#include <optional>
#include <vector>

namespace omop::common::date_utils {

// Type aliases for convenience
using time_point = std::chrono::system_clock::time_point;
using duration_type = std::chrono::milliseconds;

/**
 * @brief Parse date from string with specific format
 * @param date_str Date string
 * @param format Format string (default: "%Y-%m-%d")
 * @return Optional system_clock time_point
 */
std::optional<time_point> parse_date(const std::string& date_str, const std::string& format = "%Y-%m-%d");

/**
 * @brief Parse date with multiple format attempts
 * @param date_str Date string
 * @param formats Vector of format strings to try
 * @return Optional system_clock time_point
 */
std::optional<time_point> parse_date_multiple_formats(const std::string& date_str, const std::vector<std::string>& formats);

/**
 * @brief Format date with custom format
 * @param tp Time point to format
 * @param format Format string (default: "%Y-%m-%d")
 * @return Formatted date string
 */
std::string format_date(const time_point& tp, const std::string& format = "%Y-%m-%d");

/**
 * @brief Get current timestamp as formatted string
 * @param format Format string (default: "%Y-%m-%d %H:%M:%S")
 * @return Current timestamp string
 */
std::string current_timestamp(const std::string& format = "%Y-%m-%d %H:%M:%S");

/**
 * @brief Add duration to time point
 * @param tp Time point
 * @param days Days to add (default: 0)
 * @param hours Hours to add (default: 0)
 * @param minutes Minutes to add (default: 0)
 * @return New time point
 */
time_point add_duration(const time_point& tp, int days = 0, int hours = 0, int minutes = 0);

/**
 * @brief Calculate age in years
 * @param birth_date Birth date
 * @param as_of_date Reference date (default: now)
 * @return Age in years
 */
int calculate_age(const time_point& birth_date, const time_point& as_of_date = std::chrono::system_clock::now());

/**
 * @brief Get start of day
 * @param tp Input time point
 * @return Start of day (00:00:00)
 */
time_point start_of_day(const time_point& tp);

/**
 * @brief Get end of day
 * @param tp Input time point
 * @return End of day (23:59:59.999)
 */
time_point end_of_day(const time_point& tp);

/**
 * @brief Check if date is valid
 * @param year Year
 * @param month Month (1-12)
 * @param day Day (1-31)
 * @return true if valid date
 */
bool is_valid_date(int year, int month, int day);

/**
 * @brief Get ISO week number
 * @param tp Time point
 * @return ISO week number
 */
int get_iso_week(const time_point& tp);

/**
 * @brief Convert timezone (basic implementation)
 * @param tp Time point
 * @param from_tz Source timezone
 * @param to_tz Target timezone
 * @return Converted time point
 */
time_point convert_timezone(const time_point& tp, const std::string& from_tz, const std::string& to_tz);

/**
 * @brief Calculate duration between two time points
 * @param start Start time point
 * @param end End time point
 * @return Duration in milliseconds
 */
duration_type duration_between(const time_point& start, const time_point& end);

/**
 * @brief Format duration in human-readable format
 * @param duration Duration to format
 * @return Human-readable duration string
 */
std::string format_duration_human(const duration_type& duration);

/**
 * @brief Check if time point is weekend
 * @param tp Time point to check
 * @return true if weekend
 */
bool is_weekend(const time_point& tp);

// UK-specific formatting functions
/**
 * @brief Format date in UK format (dd/mm/yyyy)
 * @param tp Time point to format
 * @return UK formatted date string
 */
std::string format_uk_date(const time_point& tp);

/**
 * @brief Format datetime in UK format (dd/mm/yyyy hh:mm:ss)
 * @param tp Time point to format
 * @return UK formatted datetime string
 */
std::string format_uk_datetime(const time_point& tp);

/**
 * @brief Format date in ISO format (yyyy-mm-dd)
 * @param tp Time point to format
 * @return ISO formatted date string
 */
std::string format_iso_date(const time_point& tp);

/**
 * @brief Format datetime in ISO format (yyyy-mm-ddThh:mm:ss)
 * @param tp Time point to format
 * @return ISO formatted datetime string
 */
std::string format_iso_datetime(const time_point& tp);



} // namespace omop::common::date_utils