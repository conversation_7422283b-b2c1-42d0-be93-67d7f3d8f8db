/**
 * @file file_utils.cpp
 * @brief Implementation of file system utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "file_utils.h"
#include <fstream>
#include <filesystem>
#include <regex>
#include <sstream>
#include <system_error>

namespace fs = std::filesystem;

namespace omop::common::file_utils {

// File operations
std::optional<std::string> read_file(const std::filesystem::path& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        return std::nullopt;
    }
    
    std::ostringstream content;
    content << file.rdbuf();
    return content.str();
}

bool write_file(const std::filesystem::path& filepath, std::string_view content, bool append) {
    std::ofstream file(filepath, append ? std::ios::app : std::ios::trunc);
    if (!file.is_open()) {
        return false;
    }
    
    file << content;
    return file.good();
}

std::optional<std::vector<std::string>> read_lines(const std::filesystem::path& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        return std::nullopt;
    }
    
    std::vector<std::string> lines;
    std::string line;
    while (std::getline(file, line)) {
        lines.push_back(std::move(line));
    }
    
    return lines;
}

bool write_lines(const std::filesystem::path& filepath, const std::vector<std::string>& lines, bool append) {
    std::ofstream file(filepath, append ? std::ios::app : std::ios::trunc);
    if (!file.is_open()) {
        return false;
    }
    
    for (const auto& line : lines) {
        file << line << '\n';
    }
    
    return file.good();
}

std::optional<std::vector<uint8_t>> read_binary(const std::filesystem::path& filepath) {
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        return std::nullopt;
    }
    
    file.seekg(0, std::ios::end);
    auto size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<uint8_t> buffer(size);
    file.read(reinterpret_cast<char*>(buffer.data()), size);
    
    return buffer;
}

bool write_binary(const std::filesystem::path& filepath, const std::vector<uint8_t>& data, bool append) {
    std::ofstream file(filepath, std::ios::binary | (append ? std::ios::app : std::ios::trunc));
    if (!file.is_open()) {
        return false;
    }
    
    file.write(reinterpret_cast<const char*>(data.data()), data.size());
    return file.good();
}

bool append_to_file(const std::filesystem::path& filepath, std::string_view content) {
    return write_file(filepath, content, true);
}

// File information
bool exists(const std::filesystem::path& filepath) {
    return fs::exists(filepath);
}

bool is_file(const std::filesystem::path& path) {
    return fs::is_regular_file(path);
}

bool is_directory(const std::filesystem::path& path) {
    return fs::is_directory(path);
}

std::optional<std::uintmax_t> file_size(const std::filesystem::path& filepath) {
    std::error_code ec;
    auto size = fs::file_size(filepath, ec);
    if (ec) {
        return std::nullopt;
    }
    return size;
}

std::optional<std::filesystem::file_time_type> last_write_time(const std::filesystem::path& filepath) {
    std::error_code ec;
    auto time = fs::last_write_time(filepath, ec);
    if (ec) {
        return std::nullopt;
    }
    return time;
}

std::filesystem::path get_extension(const std::filesystem::path& filepath) {
    return filepath.extension();
}

std::filesystem::path get_filename(const std::filesystem::path& filepath) {
    return filepath.filename();
}

std::filesystem::path get_stem(const std::filesystem::path& filepath) {
    return filepath.stem();
}

std::filesystem::path get_parent_path(const std::filesystem::path& filepath) {
    return filepath.parent_path();
}

// Directory operations
bool create_directory(const std::filesystem::path& path) {
    std::error_code ec;
    return fs::create_directories(path, ec);
}

bool create_directory_recursive(const std::filesystem::path& path) {
    std::error_code ec;
    return fs::create_directories(path, ec);
}

bool remove_file(const std::filesystem::path& filepath) {
    std::error_code ec;
    return fs::remove(filepath, ec);
}

bool remove_directory(const std::filesystem::path& path, bool recursive) {
    std::error_code ec;
    if (recursive) {
        return fs::remove_all(path, ec) > 0;
    } else {
        return fs::remove(path, ec);
    }
}

std::optional<std::vector<std::filesystem::path>> list_directory(const std::filesystem::path& path) {
    std::error_code ec;
    std::vector<std::filesystem::path> entries;
    
    for (const auto& entry : fs::directory_iterator(path, ec)) {
        if (ec) {
            return std::nullopt;
        }
        entries.push_back(entry.path());
    }
    
    return entries;
}

std::optional<std::vector<std::filesystem::path>> list_directory_recursive(const std::filesystem::path& path) {
    std::error_code ec;
    std::vector<std::filesystem::path> entries;
    
    for (const auto& entry : fs::recursive_directory_iterator(path, ec)) {
        if (ec) {
            return std::nullopt;
        }
        entries.push_back(entry.path());
    }
    
    return entries;
}

std::vector<std::filesystem::path> find_files(const std::filesystem::path& directory, const std::string& pattern) {
    std::vector<std::filesystem::path> matching_files;
    
    try {
        std::regex regex_pattern(pattern);
        
        for (const auto& entry : fs::recursive_directory_iterator(directory)) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                if (std::regex_match(filename, regex_pattern)) {
                    matching_files.push_back(entry.path());
                }
            }
        }
    } catch (const std::exception&) {
        // Invalid regex pattern
        return {};
    }
    
    return matching_files;
}

// File operations
bool copy_file(const std::filesystem::path& source, const std::filesystem::path& destination) {
    std::error_code ec;
    return fs::copy_file(source, destination, ec);
}

bool move_file(const std::filesystem::path& source, const std::filesystem::path& destination) {
    std::error_code ec;
    fs::rename(source, destination, ec);
    return !ec;
}

bool copy_directory(const std::filesystem::path& source, const std::filesystem::path& destination) {
    std::error_code ec;
    fs::copy(source, destination, fs::copy_options::recursive, ec);
    return !ec;
}

// Temporary files
std::filesystem::path get_temp_directory() {
    return fs::temp_directory_path();
}

std::optional<std::filesystem::path> create_temp_file(const std::string& prefix, const std::string& extension) {
    auto temp_dir = get_temp_directory();
    
    // Generate unique filename
    for (int i = 0; i < 1000; ++i) {
        auto filename = prefix + "_" + std::to_string(std::time(nullptr)) + "_" + std::to_string(i) + extension;
        auto temp_path = temp_dir / filename;
        
        if (!std::filesystem::exists(temp_path)) {
            // Try to create the file
            std::ofstream file(temp_path);
            if (file.is_open()) {
                file.close();
                return temp_path;
            }
        }
    }
    
    return std::nullopt;
}

std::optional<std::filesystem::path> create_temp_directory(const std::string& prefix) {
    auto temp_dir = get_temp_directory();
    
    // Generate unique directory name
    for (int i = 0; i < 1000; ++i) {
        auto dirname = prefix + "_" + std::to_string(std::time(nullptr)) + "_" + std::to_string(i);
        auto temp_path = temp_dir / dirname;
        
        if (std::filesystem::create_directory(temp_path)) {
            return temp_path;
        }
    }
    
    return std::nullopt;
}

// Path manipulation
std::filesystem::path join_paths(const std::filesystem::path& base, const std::filesystem::path& relative) {
    return base / relative;
}

std::filesystem::path resolve_path(const std::filesystem::path& path) {
    std::error_code ec;
    auto resolved = fs::canonical(path, ec);
    if (ec) {
        return path; // Return original if resolution fails
    }
    return resolved;
}

std::filesystem::path make_relative(const std::filesystem::path& path, const std::filesystem::path& base) {
    std::error_code ec;
    auto relative = fs::relative(path, base, ec);
    if (ec) {
        return path; // Return original if can't make relative
    }
    return relative;
}

bool is_absolute_path(const std::filesystem::path& path) {
    return path.is_absolute();
}

bool is_relative_path(const std::filesystem::path& path) {
    return path.is_relative();
}

// File permissions (basic implementation)
bool is_readable(const std::filesystem::path& path) {
    std::ifstream file(path);
    return file.good();
}

bool is_writable(const std::filesystem::path& path) {
    if (!std::filesystem::exists(path)) {
        // Check if parent directory is writable by trying to create a temp file
        auto parent = path.parent_path();
        if (parent.empty()) parent = ".";
        
        auto temp_file = create_temp_file("write_test", ".tmp");
        if (temp_file) {
            remove_file(*temp_file);
            return true;
        }
        return false;
    }
    
    // For existing files, try to open for append
    std::ofstream file(path, std::ios::app);
    return file.good();
}

// Advanced operations
std::optional<std::string> calculate_file_hash(const std::filesystem::path& filepath, const std::string& algorithm) {
    // Simplified hash calculation - in production use proper crypto library
    auto content = read_binary(filepath);
    if (!content) {
        return std::nullopt;
    }
    
    // This is a placeholder - implement actual hash calculation based on algorithm
    // For now, return a simple checksum
    std::size_t hash = 0;
    for (uint8_t byte : *content) {
        hash ^= std::hash<uint8_t>{}(byte) + 0x9e3779b9 + (hash << 6) + (hash >> 2);
    }
    
    std::ostringstream oss;
    oss << std::hex << hash;
    return oss.str();
}

bool compare_files(const std::filesystem::path& file1, const std::filesystem::path& file2) {
    auto content1 = read_binary(file1);
    auto content2 = read_binary(file2);
    
    if (!content1 || !content2) {
        return false;
    }
    
    return *content1 == *content2;
}

std::optional<std::uintmax_t> get_directory_size(const std::filesystem::path& path) {
    std::error_code ec;
    std::uintmax_t size = 0;
    
    for (const auto& entry : fs::recursive_directory_iterator(path, ec)) {
        if (ec) {
            return std::nullopt;
        }
        
        if (entry.is_regular_file()) {
            try {
                size += std::filesystem::file_size(entry.path());
            } catch (const std::exception&) {
                // Ignore files we can't access
            }
        }
    }
    
    return size;
}

namespace monitoring {

FileWatcher::FileWatcher(const std::filesystem::path& path, 
                        FileWatchCallback callback,
                        std::chrono::milliseconds poll_interval)
    : watch_path_(path), callback_(std::move(callback)), poll_interval_(poll_interval) {
}

FileWatcher::~FileWatcher() {
    stop();
}

void FileWatcher::start() {
    if (running_.load()) {
        return;
    }
    
    should_stop_ = false;
    running_ = true;
    
    watch_thread_ = std::thread(&FileWatcher::watch_loop, this);
}

void FileWatcher::stop() {
    should_stop_ = true;
    running_ = false;
    if (watch_thread_.joinable()) {
        watch_thread_.join();
    }
}

void FileWatcher::watch_loop() {
    if (!operations::exists(watch_path_)) {
        return;
    }
    
    last_write_time_ = std::filesystem::last_write_time(watch_path_);
    
    while (!should_stop_) {
        std::this_thread::sleep_for(poll_interval_);
        
        if (!operations::exists(watch_path_)) {
            continue;
        }
        
        try {
            auto current_time = std::filesystem::last_write_time(watch_path_);
            if (current_time > last_write_time_) {
                last_write_time_ = current_time;
                if (callback_) {
                    callback_(watch_path_, FileEvent::Modified);
                }
            }
        } catch (const std::exception&) {
            // Ignore filesystem errors
        }
    }
}

} // namespace monitoring

// ========== COMPATIBILITY FUNCTIONS (FileUtils API from utilities.cpp) ==========

// Direct compatibility functions for legacy FileUtils class API
std::optional<std::string> read_file_legacy(const std::string& filepath) {
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        return std::nullopt;
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());
    return content;
}

bool write_file_legacy(const std::string& filepath,
                      const std::string& content,
                      bool append) {
    std::ios_base::openmode mode = std::ios::out;
    if (append) {
        mode |= std::ios::app;
    }

    std::ofstream file(filepath, mode);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    return file.good();
}

bool file_exists_legacy(const std::string& filepath) {
    return std::filesystem::exists(filepath) &&
           std::filesystem::is_regular_file(filepath);
}

std::optional<size_t> file_size_legacy(const std::string& filepath) {
    try {
        return std::filesystem::file_size(filepath);
    } catch (...) {
        return std::nullopt;
    }
}

std::string get_extension_legacy(const std::string& filepath) {
    return std::filesystem::path(filepath).extension().string();
}

std::string get_basename_legacy(const std::string& filepath) {
    return std::filesystem::path(filepath).stem().string();
}

std::string get_directory_legacy(const std::string& filepath) {
    return std::filesystem::path(filepath).parent_path().string();
}

bool create_directory_legacy(const std::string& path) {
    try {
        return std::filesystem::create_directories(path);
    } catch (...) {
        return false;
    }
}

std::vector<std::string> list_files_legacy(const std::string& directory,
                                          const std::string& pattern,
                                          bool recursive) {
    std::vector<std::string> files;
    std::regex pattern_regex(pattern);

    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), pattern_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), pattern_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (...) {
        // Return empty vector on error
    }

    return files;
}

bool copy_file_legacy(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::copy_file(source, destination,
                                  std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (...) {
        return false;
    }
}

bool move_file_legacy(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::rename(source, destination);
        return true;
    } catch (...) {
        return false;
    }
}

bool delete_file_legacy(const std::string& filepath) {
    try {
        return std::filesystem::remove(filepath);
    } catch (...) {
        return false;
    }
}

std::string get_temp_directory_legacy() {
    return std::filesystem::temp_directory_path().string();
}

std::string create_temp_file_legacy(const std::string& prefix, const std::string& extension) {
    std::string temp_dir = get_temp_directory_legacy();
    // Note: This requires string_utils::random_string - will need to include string_utils
    // For now, use timestamp-based naming
    std::string filename = prefix + "_" + std::to_string(std::time(nullptr)) + extension;
    return std::filesystem::path(temp_dir) / filename;
}

} // namespace omop::common::file_utils