/**
 * @file system_utils.h
 * @brief System-level utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <string>
#include <string_view>
#include <vector>
#include <optional>
#include <chrono>
#include <unordered_map>
#include <functional>
#include <thread>
#include <atomic>
#include <cstdint>
#include <future>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <type_traits>

namespace omop::common::system_utils {

/**
 * @brief Environment variable utilities
 */
namespace environment {

/**
 * @brief Get environment variable value
 * @param name Variable name
 * @return Variable value or nullopt if not set
 */
std::optional<std::string> get(std::string_view name);

/**
 * @brief Set environment variable
 * @param name Variable name
 * @param value Variable value
 * @return True if successful
 */
bool set(std::string_view name, std::string_view value);

/**
 * @brief Unset environment variable
 * @param name Variable name
 * @return True if successful
 */
bool unset(std::string_view name);

/**
 * @brief Check if environment variable exists
 * @param name Variable name
 * @return True if exists
 */
bool exists(std::string_view name);

/**
 * @brief Get all environment variables
 * @return Map of environment variables
 */
std::unordered_map<std::string, std::string> get_all();

/**
 * @brief Expand environment variables in string
 * @param input String with ${VAR} or $VAR patterns
 * @return String with variables expanded
 */
std::string expand_variables(const std::string& input);

/**
 * @brief Get user's home directory
 * @return Home directory path
 */
std::optional<std::string> get_home_directory();

/**
 * @brief Get user's config directory
 * @return Config directory path
 */
std::optional<std::string> get_config_directory();

/**
 * @brief Get temporary directory
 * @return Temporary directory path
 */
std::optional<std::string> get_temp_directory();

} // namespace environment

/**
 * @brief Process utilities
 */
namespace process {

/**
 * @brief Get current process ID
 * @return Process ID
 */
uint32_t get_pid();

/**
 * @brief Get parent process ID
 * @return Parent process ID
 */
uint32_t get_parent_pid();

/**
 * @brief Get current user ID
 * @return User ID
 */
uint32_t get_uid();

/**
 * @brief Get current group ID
 * @return Group ID
 */
uint32_t get_gid();

/**
 * @brief Get current username
 * @return Username or nullopt if error
 */
std::optional<std::string> get_username();

/**
 * @brief Get process working directory
 * @return Working directory path
 */
std::optional<std::string> get_working_directory();

/**
 * @brief Change working directory
 * @param path New working directory
 * @return True if successful
 */
bool change_working_directory(const std::string& path);

/**
 * @brief Execute command and capture output
 * @param command Command to execute
 * @param timeout Command timeout
 * @return Command output or nullopt if error
 */
std::optional<std::string> execute_command(const std::string& command,
                                          std::chrono::seconds timeout = std::chrono::seconds(30));

/**
 * @brief Execute command with input and capture output
 * @param command Command to execute
 * @param input Input to send to command
 * @param timeout Command timeout
 * @return Command output or nullopt if error
 */
std::optional<std::string> execute_command_with_input(const std::string& command,
                                                     const std::string& input,
                                                     std::chrono::seconds timeout = std::chrono::seconds(30));

/**
 * @brief Check if process exists
 * @param pid Process ID to check
 * @return True if process exists
 */
bool process_exists(uint32_t pid);

/**
 * @brief Kill process
 * @param pid Process ID to kill
 * @param signal Signal to send (default: SIGTERM)
 * @return True if successful
 */
bool kill_process(uint32_t pid, int signal = 15);

/**
 * @brief Get process memory usage
 * @param pid Process ID (0 for current process)
 * @return Memory usage in bytes or nullopt if error
 */
std::optional<size_t> get_memory_usage(uint32_t pid = 0);

/**
 * @brief Get process CPU usage percentage
 * @param pid Process ID (0 for current process)
 * @return CPU usage percentage or nullopt if error
 */
std::optional<double> get_cpu_usage(uint32_t pid = 0);

} // namespace process

/**
 * @brief System information utilities
 */
namespace info {

/**
 * @brief Get system hostname
 * @return Hostname or nullopt if error
 */
std::optional<std::string> get_hostname();

/**
 * @brief Get system architecture
 * @return Architecture string (e.g., "x86_64", "arm64")
 */
std::optional<std::string> get_architecture();

/**
 * @brief Get operating system name
 * @return OS name (e.g., "Linux", "Darwin", "Windows")
 */
std::optional<std::string> get_os_name();

/**
 * @brief Get OS version
 * @return OS version string
 */
std::optional<std::string> get_os_version();

/**
 * @brief Get kernel version
 * @return Kernel version string
 */
std::optional<std::string> get_kernel_version();

/**
 * @brief Get number of CPU cores
 * @return Number of logical CPU cores
 */
uint32_t get_cpu_count();

/**
 * @brief Get total system memory
 * @return Total memory in bytes
 */
std::optional<size_t> get_total_memory();

/**
 * @brief Get available system memory
 * @return Available memory in bytes
 */
std::optional<size_t> get_available_memory();

/**
 * @brief Get system uptime
 * @return System uptime duration
 */
std::optional<std::chrono::seconds> get_uptime();

/**
 * @brief Get system load average
 * @return Load averages [1min, 5min, 15min]
 */
std::optional<std::vector<double>> get_load_average();

/**
 * @brief Get disk space information
 * @param path Path to check
 * @return [total, available, used] in bytes
 */
std::optional<std::vector<size_t>> get_disk_space(const std::string& path);

/**
 * @brief Get network interfaces
 * @return Map of interface names to IP addresses
 */
std::unordered_map<std::string, std::vector<std::string>> get_network_interfaces();

/**
 * @brief Get system timezone
 * @return Timezone string
 */
std::optional<std::string> get_timezone();

} // namespace info

/**
 * @brief Performance monitoring utilities
 */
namespace performance {

/**
 * @brief High-resolution timer
 */
class Timer {
public:
    /**
     * @brief Start the timer
     */
    void start();
    
    /**
     * @brief Stop the timer
     * @return Elapsed time in nanoseconds
     */
    std::chrono::nanoseconds stop();
    
    /**
     * @brief Get elapsed time without stopping
     * @return Elapsed time in nanoseconds
     */
    std::chrono::nanoseconds elapsed() const;
    
    /**
     * @brief Reset the timer
     */
    void reset();
    
    /**
     * @brief Check if timer is running
     * @return True if running
     */
    bool is_running() const { return running_; }
    
private:
    std::chrono::high_resolution_clock::time_point start_time_;
    bool running_{false};
};

/**
 * @brief CPU usage monitor
 */
class CPUMonitor {
public:
    /**
     * @brief Constructor
     * @param update_interval Update interval for measurements
     */
    explicit CPUMonitor(std::chrono::milliseconds update_interval = std::chrono::milliseconds(1000));
    
    /**
     * @brief Destructor
     */
    ~CPUMonitor();
    
    /**
     * @brief Start monitoring
     */
    void start();
    
    /**
     * @brief Stop monitoring
     */
    void stop();
    
    /**
     * @brief Get current CPU usage
     * @return CPU usage percentage (0.0 - 100.0)
     */
    double get_usage() const { return current_usage_.load(); }
    
    /**
     * @brief Get average CPU usage over time
     * @param duration Duration to average over
     * @return Average CPU usage percentage
     */
    double get_average_usage(std::chrono::minutes duration = std::chrono::minutes(1)) const;
    
    /**
     * @brief Get peak CPU usage
     * @return Peak CPU usage percentage
     */
    double get_peak_usage() const { return peak_usage_.load(); }
    
    /**
     * @brief Reset statistics
     */
    void reset();
    
private:
    std::chrono::milliseconds update_interval_;
    std::atomic<double> current_usage_{0.0};
    std::atomic<double> peak_usage_{0.0};
    
    std::thread monitor_thread_;
    std::atomic<bool> running_{false};
    std::atomic<bool> should_stop_{false};
    
    // Historical data for averaging
    mutable std::mutex history_mutex_;
    std::vector<std::pair<std::chrono::steady_clock::time_point, double>> usage_history_;
    
    void monitor_loop();
    double calculate_cpu_usage();
};

/**
 * @brief Memory usage monitor
 */
class MemoryMonitor {
public:
    /**
     * @brief Constructor
     * @param update_interval Update interval for measurements
     */
    explicit MemoryMonitor(std::chrono::milliseconds update_interval = std::chrono::milliseconds(1000));
    
    /**
     * @brief Destructor
     */
    ~MemoryMonitor();
    
    /**
     * @brief Start monitoring
     */
    void start();
    
    /**
     * @brief Stop monitoring
     */
    void stop();
    
    /**
     * @brief Get current memory usage
     * @return Memory usage in bytes
     */
    size_t get_usage() const { return current_usage_.load(); }
    
    /**
     * @brief Get peak memory usage
     * @return Peak memory usage in bytes
     */
    size_t get_peak_usage() const { return peak_usage_.load(); }
    
    /**
     * @brief Get average memory usage
     * @param duration Duration to average over
     * @return Average memory usage in bytes
     */
    size_t get_average_usage(std::chrono::minutes duration = std::chrono::minutes(1)) const;
    
    /**
     * @brief Reset statistics
     */
    void reset();
    
private:
    std::chrono::milliseconds update_interval_;
    std::atomic<size_t> current_usage_{0};
    std::atomic<size_t> peak_usage_{0};
    
    std::thread monitor_thread_;
    std::atomic<bool> running_{false};
    std::atomic<bool> should_stop_{false};
    
    // Historical data for averaging
    mutable std::mutex history_mutex_;
    std::vector<std::pair<std::chrono::steady_clock::time_point, size_t>> usage_history_;
    
    void monitor_loop();
};

/**
 * @brief System resource monitor
 */
class ResourceMonitor {
public:
    /**
     * @brief Resource usage snapshot
     */
    struct ResourceUsage {
        double cpu_usage;           // CPU usage percentage
        size_t memory_usage;        // Memory usage in bytes
        size_t memory_available;    // Available memory in bytes
        double load_1min;           // 1-minute load average
        size_t disk_usage;          // Disk usage in bytes (for monitored path)
        size_t disk_available;      // Available disk space in bytes
        std::chrono::system_clock::time_point timestamp;
    };
    
    /**
     * @brief Constructor
     * @param disk_path Path to monitor disk usage
     * @param update_interval Update interval
     */
    explicit ResourceMonitor(const std::string& disk_path = "/",
                           std::chrono::milliseconds update_interval = std::chrono::milliseconds(5000));
    
    /**
     * @brief Destructor
     */
    ~ResourceMonitor();
    
    /**
     * @brief Start monitoring
     */
    void start();
    
    /**
     * @brief Stop monitoring
     */
    void stop();
    
    /**
     * @brief Get current resource usage
     * @return Current usage snapshot
     */
    ResourceUsage get_current_usage() const;
    
    /**
     * @brief Get resource usage history
     * @param duration Duration to get history for
     * @return Vector of usage snapshots
     */
    std::vector<ResourceUsage> get_history(std::chrono::minutes duration = std::chrono::minutes(10)) const;
    
    /**
     * @brief Set resource usage callback
     * @param callback Callback function called on each update
     */
    void set_callback(std::function<void(const ResourceUsage&)> callback) {
        callback_ = std::move(callback);
    }
    
    /**
     * @brief Check if system is under high load
     * @param cpu_threshold CPU usage threshold percentage
     * @param memory_threshold Memory usage threshold percentage
     * @return True if under high load
     */
    bool is_high_load(double cpu_threshold = 80.0, double memory_threshold = 90.0) const;
    
private:
    std::string disk_path_;
    std::chrono::milliseconds update_interval_;
    std::function<void(const ResourceUsage&)> callback_;
    
    std::thread monitor_thread_;
    std::atomic<bool> running_{false};
    std::atomic<bool> should_stop_{false};
    
    mutable std::mutex usage_mutex_;
    ResourceUsage current_usage_;
    std::vector<ResourceUsage> usage_history_;
    
    void monitor_loop();
    ResourceUsage collect_usage();
};

} // namespace performance

/**
 * @brief Signal handling utilities
 */
namespace signals {

/**
 * @brief Signal handler function type
 */
using SignalHandler = std::function<void(int)>;

/**
 * @brief Install signal handler
 * @param signal Signal number
 * @param handler Handler function
 * @return True if successful
 */
bool install_handler(int signal, SignalHandler handler);

/**
 * @brief Install default signal handlers for common signals
 * @param handlers Map of signal to handler
 * @return True if all handlers installed successfully
 */
bool install_default_handlers(const std::unordered_map<int, SignalHandler>& handlers = {});

/**
 * @brief Block signals
 * @param signals Vector of signal numbers to block
 * @return True if successful
 */
bool block_signals(const std::vector<int>& signals);

/**
 * @brief Unblock signals
 * @param signals Vector of signal numbers to unblock
 * @return True if successful
 */
bool unblock_signals(const std::vector<int>& signals);

/**
 * @brief Wait for signal
 * @param signals Vector of signals to wait for
 * @param timeout Maximum wait time
 * @return Signal number or -1 if timeout
 */
int wait_for_signal(const std::vector<int>& signals, std::chrono::seconds timeout = std::chrono::seconds(0));

} // namespace signals

/**
 * @brief Threading utilities
 */
namespace threading {

/**
 * @brief Get optimal thread count for CPU-bound tasks
 * @param task_overhead Task overhead factor (0.0 - 1.0)
 * @return Optimal thread count
 */
uint32_t get_optimal_thread_count(double task_overhead = 0.1);

/**
 * @brief Set thread name
 * @param name Thread name
 * @return True if successful
 */
bool set_thread_name(const std::string& name);

/**
 * @brief Get thread name
 * @return Thread name or nullopt if error
 */
std::optional<std::string> get_thread_name();

/**
 * @brief Set thread CPU affinity
 * @param cpu_ids Vector of CPU IDs to bind to
 * @return True if successful
 */
bool set_cpu_affinity(const std::vector<uint32_t>& cpu_ids);

/**
 * @brief Get thread CPU affinity
 * @return Vector of CPU IDs or nullopt if error
 */
std::optional<std::vector<uint32_t>> get_cpu_affinity();

/**
 * @brief Thread pool for executing tasks
 * @tparam Task Task function type
 */
template<typename Task = std::function<void()>>
class ThreadPool {
public:
    /**
     * @brief Constructor
     * @param num_threads Number of worker threads
     */
    explicit ThreadPool(uint32_t num_threads = get_optimal_thread_count());
    
    /**
     * @brief Destructor
     */
    ~ThreadPool();
    
    /**
     * @brief Submit task for execution
     * @param task Task to execute
     * @return Future for task result
     */
    template<typename F, typename... Args>
    auto submit(F&& f, Args&&... args) -> std::future<typename std::invoke_result<F, Args...>::type>;
    
    /**
     * @brief Get number of worker threads
     * @return Number of threads
     */
    uint32_t thread_count() const { return workers_.size(); }
    
    /**
     * @brief Get number of pending tasks
     * @return Number of tasks in queue
     */
    size_t pending_tasks() const;
    
    /**
     * @brief Wait for all tasks to complete
     */
    void wait_for_all();
    
    /**
     * @brief Shutdown thread pool
     */
    void shutdown();
    
private:
    std::vector<std::thread> workers_;
    std::queue<std::function<void()>> tasks_;
    
    std::mutex queue_mutex_;
    std::condition_variable condition_;
    std::atomic<bool> stop_{false};
    
    void worker_loop();
};

} // namespace threading

/**
 * @brief System utilities constants
 */
namespace constants {

// Common signal numbers
constexpr int SIGNAL_INT = 2;    // SIGINT
constexpr int SIGNAL_TERM = 15;  // SIGTERM
constexpr int SIGNAL_KILL = 9;   // SIGKILL
constexpr int SIGNAL_USR1 = 10;  // SIGUSR1
constexpr int SIGNAL_USR2 = 12;  // SIGUSR2

// Memory size constants
constexpr size_t KB = 1024;
constexpr size_t MB = 1024 * KB;
constexpr size_t GB = 1024 * MB;
constexpr size_t TB = 1024 * GB;

// Time constants
constexpr std::chrono::milliseconds DEFAULT_TIMEOUT{30000};
constexpr std::chrono::milliseconds DEFAULT_POLL_INTERVAL{1000};

} // namespace constants



} // namespace omop::common::system_utils