#!/bin/bash

# OMOP ETL Test Environment Setup Script
# Sets up consistent database environment variables for all test suites

echo "=== OMOP ETL Test Environment Setup ==="

# Check if Docker containers are running
echo "Checking Docker containers..."
if ! docker ps | grep -q "omop-cdm-db\|clinical-db"; then
    echo "ERROR: Required Docker containers not running!"
    echo "Please run: DEV_ENABLE_TESTS=true docker-compose -f scripts/docker-compose.yml --profile dev up -d"
    exit 1
fi

# Detect if we're running inside Docker containers or on host
if [ -n "${DOCKER_CONTAINER:-}" ] || [ -f /.dockerenv ]; then
    # Running inside Docker - use container names
    echo "Detected Docker environment - using container names"
    
    # Set OMOP CDM Database Environment Variables
    export OMOP_HOST=omop-db
    export OMOP_PORT=5432
    export OMOP_DB=omop_cdm
    export OMOP_USER=omop_user
    export OMOP_PASSWORD=omop_pass
    
    # Set Clinical Database Environment Variables
    export POSTGRES_HOST=clinical-db
    export POSTGRES_PORT=5432
    export POSTGRES_DB=clinical_db
    export POSTGRES_USER=clinical_user
    export POSTGRES_PASSWORD=clinical_pass
    
    # Set Test Database Environment Variables (use clinical DB for tests)
    export TEST_DB_HOST=clinical-db
    export TEST_DB_PORT=5432
    export TEST_DB_NAME=clinical_db
    export TEST_DB_USER=clinical_user
    export TEST_DB_PASSWORD=clinical_pass
    
    # Set MySQL Database Environment Variables
    export MYSQL_HOST=mysql-db
    export MYSQL_PORT=3306
    export MYSQL_DATABASE=mysql_clinical_db
    export MYSQL_USER=mysql_clinical_user
    export MYSQL_PASSWORD=mysql_clinical_pass
else
    # Running on host - use localhost with mapped ports
    echo "Detected host environment - using localhost with mapped ports"
    
    # Set OMOP CDM Database Environment Variables (Port 5433 mapped from container)
    export OMOP_HOST=localhost
    export OMOP_PORT=5433
    export OMOP_DB=omop_cdm
    export OMOP_USER=omop_user
    export OMOP_PASSWORD=omop_pass
    
    # Set Clinical Database Environment Variables (Port 5432 mapped from container)
    export POSTGRES_HOST=localhost
    export POSTGRES_PORT=5432
    export POSTGRES_DB=clinical_db
    export POSTGRES_USER=clinical_user
    export POSTGRES_PASSWORD=clinical_pass
    
    # Set Test Database Environment Variables (use clinical DB for tests)
    export TEST_DB_HOST=localhost
    export TEST_DB_PORT=5432
    export TEST_DB_NAME=clinical_db
    export TEST_DB_USER=clinical_user
    export TEST_DB_PASSWORD=clinical_pass
    
    # Set MySQL Database Environment Variables (Port 3306)
    export MYSQL_HOST=localhost
    export MYSQL_PORT=3306
    export MYSQL_DATABASE=mysql_clinical_db
    export MYSQL_USER=mysql_clinical_user
    export MYSQL_PASSWORD=mysql_clinical_pass
fi

# Set Test Control Environment Variables
export OMOP_RUN_INTEGRATION_TESTS=1
export TEST_DATA_DIR="/Users/<USER>/uclwork/etl/omop-etl/tests/integration/test_data"

# Enable verbose logging for debugging
export SPDLOG_LEVEL=info

echo "Environment variables set:"
echo "  OMOP Database: ${OMOP_HOST}:${OMOP_PORT}/${OMOP_DB} (user: ${OMOP_USER})"
echo "  Clinical Database: ${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB} (user: ${POSTGRES_USER})"
echo "  Test Database: ${TEST_DB_HOST}:${TEST_DB_PORT}/${TEST_DB_NAME} (user: ${TEST_DB_USER})"
echo "  MySQL Database: ${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE} (user: ${MYSQL_USER})"

# Test database connectivity
echo ""
echo "Testing database connectivity..."

# Test OMOP database
if timeout 5 psql -h ${OMOP_HOST} -p ${OMOP_PORT} -U ${OMOP_USER} -d ${OMOP_DB} -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ OMOP database connection: SUCCESS"
else
    echo "❌ OMOP database connection: FAILED"
fi

# Test Clinical database  
if timeout 5 psql -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Clinical database connection: SUCCESS"
else
    echo "❌ Clinical database connection: FAILED"
fi

echo ""
echo "=== Environment Setup Complete ==="
echo "You can now run tests with consistent database connections."
echo ""
echo "Example usage:"
echo "  source scripts/setup-test-env.sh"
echo "  ./build/bin/load_integration_tests --gtest_brief=1"
echo "  ./build/bin/cdm_integration_tests --gtest_brief=1"